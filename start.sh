#!/bin/bash

# AI Logo Tool 启动脚本

echo "🚀 启动 AI Logo Tool..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查.env文件是否存在
if [ ! -f .env ]; then
    echo "⚠️  .env文件不存在，从模板创建..."
    cp .env.example .env
    echo "📝 请编辑 .env 文件，填入你的API密钥"
    echo "   - OPENAI_API_KEY (OpenAI API密钥)"
    echo "   - STABILITY_API_KEY (Stability AI API密钥)"
    echo "   - JWT_SECRET_KEY (JWT加密密钥)"
    read -p "按回车键继续..."
fi

# 创建必要的目录
mkdir -p uploads
mkdir -p nginx

# 启动服务
echo "🐳 启动Docker容器..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

echo ""
echo "✅ 服务启动完成！"
echo ""
echo "🌐 访问地址："
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://localhost:8000"
echo "   API文档:  http://localhost:8000/docs"
echo ""
echo "📋 常用命令："
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo ""
echo "🔧 如需修改配置，请编辑 .env 文件后重启服务"
