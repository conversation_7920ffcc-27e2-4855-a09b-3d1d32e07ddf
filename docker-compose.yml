version: '3.8'

services:
  # 前端服务 - Vue3 + TypeScript
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8000
    depends_on:
      - backend
    networks:
      - ai-logo-network

  # 后端服务 - Python FastAPI
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    environment:
      - MONGODB_URL=mongodb://mongodb:27017/ai_logo_tool
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - STABILITY_API_KEY=${STABILITY_API_KEY}
    depends_on:
      - mongodb
      - redis
    networks:
      - ai-logo-network

  # MongoDB 数据库
  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    environment:
      - MONGO_INITDB_DATABASE=ai_logo_tool
    networks:
      - ai-logo-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-logo-network

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./uploads:/var/www/uploads
    depends_on:
      - frontend
      - backend
    networks:
      - ai-logo-network

volumes:
  mongodb_data:
  redis_data:

networks:
  ai-logo-network:
    driver: bridge
