from fastapi import FastAPI, HTTPException, File, UploadFile, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
import os
from dotenv import load_dotenv
import requests
import uuid
import json
import time
from datetime import datetime
from typing import Optional, List, Tuple
from PIL import Image

# 加载环境变量
load_dotenv()

app = FastAPI(title="AI Logo Tool API", version="2.0.0", description="AI驱动的智能LOGO设计工具")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:80"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件服务
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# RunningHub AI配置
RUNNINGHUB_API_KEY = os.getenv("RUNNINGHUB_API_KEY", "906e221ce02f477c8926205f44b0b5b9")
RUNNINGHUB_API_URL = os.getenv("RUNNINGHUB_API_URL", "https://www.runninghub.cn")
RUNNINGHUB_ID = os.getenv("RUNNINGHUB_ID", "1931900822227976194")

# 确保上传目录存在
os.makedirs("uploads", exist_ok=True)

# 行业专业化模板
INDUSTRY_TEMPLATES = {
    'technology': {
        'keywords': ['modern', 'digital', 'innovation', 'tech', 'futuristic', 'sleek', 'cutting-edge'],
        'colors': ['blue tones', 'gray tones', 'blue and silver'],
        'styles': ['minimalist', 'geometric', 'modern'],
        'avoid': ['vintage', 'handdrawn', 'organic'],
        'shapes': 'geometric shapes, clean lines, angular design'
    },
    'food': {
        'keywords': ['delicious', 'fresh', 'organic', 'tasty', 'appetizing', 'warm', 'inviting'],
        'colors': ['red tones', 'orange tones', 'warm colors', 'earth tones'],
        'styles': ['vintage', 'handdrawn', 'modern', 'cartoon'],
        'avoid': ['industrial', 'cold', 'mechanical'],
        'shapes': 'rounded shapes, organic forms, flowing lines'
    },
    'healthcare': {
        'keywords': ['trust', 'care', 'health', 'medical', 'professional', 'clean', 'safe'],
        'colors': ['blue tones', 'green tones', 'white and blue'],
        'styles': ['minimalist', 'modern', 'corporate'],
        'avoid': ['dark', 'aggressive', 'chaotic'],
        'shapes': 'cross symbols, heart shapes, protective shields'
    },
    'education': {
        'keywords': ['knowledge', 'learning', 'growth', 'wisdom', 'academic', 'bright', 'inspiring'],
        'colors': ['blue tones', 'green tones', 'colorful'],
        'styles': ['modern', 'cartoon', 'handdrawn'],
        'avoid': ['dark', 'aggressive', 'complex'],
        'shapes': 'book symbols, graduation caps, tree of knowledge'
    },
    'finance': {
        'keywords': ['trust', 'stability', 'growth', 'professional', 'secure', 'reliable', 'premium'],
        'colors': ['blue tones', 'green tones', 'gold accents'],
        'styles': ['corporate', 'minimalist', 'modern'],
        'avoid': ['playful', 'cartoon', 'chaotic'],
        'shapes': 'upward arrows, building silhouettes, shield symbols'
    },
    'fashion': {
        'keywords': ['elegant', 'stylish', 'trendy', 'chic', 'sophisticated', 'luxury', 'beautiful'],
        'colors': ['black and white', 'purple tones', 'gold accents'],
        'styles': ['minimalist', 'vintage', 'modern'],
        'avoid': ['technical', 'industrial', 'rough'],
        'shapes': 'flowing lines, elegant curves, fashion silhouettes'
    },
    'real_estate': {
        'keywords': ['trust', 'home', 'property', 'investment', 'solid', 'reliable', 'premium'],
        'colors': ['blue tones', 'brown tones', 'gold accents'],
        'styles': ['corporate', 'modern', 'minimalist'],
        'avoid': ['playful', 'cartoon', 'chaotic'],
        'shapes': 'house silhouettes, building outlines, key symbols'
    },
    'automotive': {
        'keywords': ['speed', 'power', 'precision', 'performance', 'dynamic', 'reliable', 'innovative'],
        'colors': ['red tones', 'black and silver', 'blue tones'],
        'styles': ['modern', 'geometric', 'minimalist'],
        'avoid': ['soft', 'organic', 'vintage'],
        'shapes': 'angular designs, speed lines, wheel elements'
    },
    'sports': {
        'keywords': ['energy', 'strength', 'victory', 'dynamic', 'athletic', 'competitive', 'powerful'],
        'colors': ['red tones', 'orange tones', 'bold colors'],
        'styles': ['modern', 'geometric', 'abstract'],
        'avoid': ['delicate', 'soft', 'vintage'],
        'shapes': 'dynamic shapes, movement lines, trophy elements'
    },
    'beauty': {
        'keywords': ['elegant', 'beautiful', 'luxurious', 'refined', 'graceful', 'premium', 'sophisticated'],
        'colors': ['pink tones', 'gold accents', 'purple tones'],
        'styles': ['minimalist', 'vintage', 'modern'],
        'avoid': ['rough', 'industrial', 'technical'],
        'shapes': 'flowing curves, floral elements, elegant lines'
    },
    'travel': {
        'keywords': ['adventure', 'journey', 'exploration', 'freedom', 'discovery', 'wanderlust', 'global'],
        'colors': ['blue tones', 'green tones', 'warm colors'],
        'styles': ['modern', 'vintage', 'handdrawn'],
        'avoid': ['static', 'confined', 'heavy'],
        'shapes': 'compass symbols, globe elements, path lines'
    },
    'entertainment': {
        'keywords': ['fun', 'exciting', 'creative', 'vibrant', 'engaging', 'dynamic', 'colorful'],
        'colors': ['bright colors', 'rainbow tones', 'neon colors'],
        'styles': ['cartoon', 'modern', 'abstract'],
        'avoid': ['boring', 'corporate', 'dull'],
        'shapes': 'playful shapes, star elements, dynamic forms'
    },
    'consulting': {
        'keywords': ['professional', 'expert', 'strategic', 'intelligent', 'trusted', 'advisory', 'solution'],
        'colors': ['blue tones', 'gray tones', 'navy blue'],
        'styles': ['corporate', 'minimalist', 'modern'],
        'avoid': ['playful', 'cartoon', 'casual'],
        'shapes': 'geometric patterns, arrow symbols, network connections'
    },
    'retail': {
        'keywords': ['shopping', 'quality', 'variety', 'customer', 'service', 'value', 'convenient'],
        'colors': ['warm colors', 'red tones', 'orange tones'],
        'styles': ['modern', 'minimalist', 'cartoon'],
        'avoid': ['complex', 'dark', 'intimidating'],
        'shapes': 'shopping bag symbols, store fronts, price tags'
    },
    'construction': {
        'keywords': ['strong', 'reliable', 'solid', 'building', 'foundation', 'durable', 'professional'],
        'colors': ['orange tones', 'yellow tones', 'gray tones'],
        'styles': ['corporate', 'geometric', 'modern'],
        'avoid': ['delicate', 'soft', 'fragile'],
        'shapes': 'building blocks, construction tools, structural elements'
    }
}

# 风格变体配置
STYLE_VARIANTS = {
    'minimalist': [
        'clean minimalist design',
        'ultra-minimal simple design',
        'geometric minimal style'
    ],
    'modern': [
        'contemporary modern design',
        'sleek modern style',
        'cutting-edge modern look'
    ],
    'vintage': [
        'classic vintage style',
        'retro vintage design',
        'nostalgic vintage look'
    ]
}

# 数据模型
class NodeParameter(BaseModel):
    node_id: str
    field_name: str
    field_value: str

class LogoRequest(BaseModel):
    prompt: str
    style: str = "minimalist"
    width: int = 1024
    height: int = 1024
    company_name: Optional[str] = None
    industry: Optional[str] = None
    keywords: Optional[str] = None
    color_scheme: Optional[str] = None
    custom_parameters: Optional[List[NodeParameter]] = None  # 允许用户自定义节点参数

class LogoResponse(BaseModel):
    success: bool
    image_url: Optional[str] = None
    image_id: Optional[str] = None
    prompt_used: Optional[str] = None
    style_used: Optional[str] = None
    generation_time: Optional[float] = None
    error: Optional[str] = None

class LogoVariant(BaseModel):
    id: str
    image_url: str
    prompt_used: str
    style_name: str
    description: str
    generation_time: float

class MultiLogoResponse(BaseModel):
    success: bool
    variants: List[LogoVariant] = []
    total_generation_time: Optional[float] = None
    error: Optional[str] = None

class LogoHistory(BaseModel):
    id: str
    prompt: str
    style: str
    image_url: str
    created_at: datetime
    company_name: Optional[str] = None
    industry: Optional[str] = None

class StyleInfo(BaseModel):
    id: str
    name: str
    description: str
    example_prompt: str

# 工具函数
def get_workflow_parameters(workflow_id: str, api_key: str) -> dict:
    """获取工作流的参数信息，包括可配置的节点"""
    url = f"{RUNNINGHUB_API_URL}/api/openapi/getJsonApiFormat"
    headers = {
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Host": "www.runninghub.cn",
        "Connection": "keep-alive"
    }

    payload = {
        "apiKey": api_key,
        "workflowId": workflow_id
    }

    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()

        result = response.json()
        if result.get("code") != 0:
            raise Exception(f"API error: {result.get('msg', 'Unknown error')}")

        workflow_data = result.get("data", {})
        prompt_json = workflow_data.get("prompt")

        if not prompt_json:
            raise Exception("No workflow data found")

        # 解析工作流JSON
        workflow = json.loads(prompt_json)

        # 提取可配置的参数
        configurable_params = {}
        for node_id, node_data in workflow.items():
            class_type = node_data.get("class_type", "")
            inputs = node_data.get("inputs", {})

            # 专门查找CLIP文本编码节点
            if "CLIP" in class_type.upper() and "TEXT" in class_type.upper():
                # CLIP文本编码节点通常有text字段
                if "text" in inputs:
                    configurable_params[f"{node_id}_text"] = {
                        "type": "text",
                        "field_name": "text",
                        "current_value": inputs["text"] if isinstance(inputs["text"], str) else str(inputs["text"]),
                        "class_type": class_type,
                        "description": f"Text prompt for {class_type}",
                        "is_clip_text": True
                    }

            # 查找其他文本输入节点
            text_fields = ["text", "prompt", "positive", "negative"]
            for field in text_fields:
                if field in inputs and isinstance(inputs[field], str):
                    param_key = f"{node_id}_{field}"
                    if param_key not in configurable_params:  # 避免重复添加CLIP节点
                        configurable_params[param_key] = {
                            "type": "text",
                            "field_name": field,
                            "current_value": inputs[field],
                            "class_type": class_type,
                            "description": f"{field} input for {class_type}",
                            "is_clip_text": False
                        }

            # 查找其他常见参数
            for param_name in ["seed", "steps", "cfg", "width", "height", "batch_size", "denoise"]:
                if param_name in inputs and isinstance(inputs[param_name], (int, float)):
                    configurable_params[f"{node_id}_{param_name}"] = {
                        "type": "number",
                        "field_name": param_name,
                        "current_value": inputs[param_name],
                        "class_type": class_type,
                        "description": f"{param_name} parameter for {class_type}"
                    }

        return {
            "workflow_id": workflow_id,
            "parameters": configurable_params,
            "total_nodes": len(workflow)
        }

    except Exception as e:
        print(f"Error getting workflow parameters: {e}")
        return {"error": str(e)}

def enhance_prompt(request: LogoRequest, variant_index: int = 0) -> str:
    """基于用户参数构建全新的LOGO生成提示词"""

    # 从用户参数重新构建提示词，忽略原有的prompt字段
    prompt_parts = []

    # 1. 公司名称 + logo（核心部分）
    if request.company_name:
        prompt_parts.append(f"{request.company_name} logo")
    else:
        prompt_parts.append("logo design")

    # 2. 获取行业模板
    industry_template = INDUSTRY_TEMPLATES.get(request.industry or "", {})

    # 3. 添加行业信息
    if request.industry:
        prompt_parts.append(f"{request.industry} industry")

    # 4. 收集所有关键词（避免重复）
    all_keywords = []

    # 添加行业特定关键词
    if request.industry and industry_template.get('keywords'):
        industry_keywords = industry_template['keywords'][:3]  # 取前3个关键词
        all_keywords.extend(industry_keywords)

    # 添加用户关键词
    if request.keywords:
        # 分割用户关键词并清理
        user_keywords = [kw.strip() for kw in request.keywords.split(',') if kw.strip()]
        all_keywords.extend(user_keywords)

    # 去重并保持顺序
    unique_keywords = []
    seen = set()
    for keyword in all_keywords:
        keyword_lower = keyword.lower()
        if keyword_lower not in seen:
            unique_keywords.append(keyword)
            seen.add(keyword_lower)

    # 添加去重后的关键词
    prompt_parts.extend(unique_keywords)

    # 5. 添加风格描述（支持变体）
    style_descriptions = {
        "minimalist": "clean, simple, modern, minimal design",
        "vintage": "retro, classic, vintage style, aged look",
        "modern": "contemporary, sleek, cutting-edge design",
        "cartoon": "playful, fun, cartoon style, colorful",
        "abstract": "abstract, artistic, creative, unique shapes",
        "geometric": "geometric shapes, structured, mathematical",
        "corporate": "professional, business, corporate identity",
        "handdrawn": "hand-drawn, artistic, sketch style, organic"
    }

    # 使用风格变体
    if request.style in STYLE_VARIANTS and variant_index < len(STYLE_VARIANTS[request.style]):
        style_desc = STYLE_VARIANTS[request.style][variant_index]
    else:
        style_desc = style_descriptions.get(request.style, "professional")

    prompt_parts.append(style_desc)

    # 6. 添加行业特定形状描述
    if industry_template.get('shapes'):
        prompt_parts.append(industry_template['shapes'])

    # 7. 添加LOGO特定要求
    prompt_parts.extend([
        "logo design",
        "vector style",
        "clean background",
        "high quality",
        "professional"
    ])

    # 8. 智能颜色方案选择
    color_scheme = request.color_scheme
    if not color_scheme and industry_template.get('colors'):
        # 如果用户没有选择颜色，使用行业推荐颜色
        color_scheme = industry_template['colors'][variant_index % len(industry_template['colors'])]

    if color_scheme:
        prompt_parts.append(f"{color_scheme} color scheme")

    # 9. 组合所有部分，用逗号分隔
    final_prompt = ", ".join(prompt_parts)

    return final_prompt

def generate_multiple_prompts(request: LogoRequest) -> List[str]:
    """生成多个不同的提示词变体"""
    prompts = []

    # 生成3个不同的变体
    for i in range(3):
        prompt = enhance_prompt(request, i)
        prompts.append(prompt)

    return prompts

@app.get("/")
async def root():
    return {
        "message": "AI Logo Tool API",
        "status": "running",
        "version": "2.0.0",
        "features": ["AI Logo Generation", "File Upload/Download", "Style Management"]
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "api_configured": bool(RUNNINGHUB_API_KEY),
        "runninghub_key_present": len(RUNNINGHUB_API_KEY) > 0 if RUNNINGHUB_API_KEY else False,
        "upload_directory": os.path.exists("uploads"),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/workflow-parameters")
async def get_workflow_parameters_endpoint():
    """获取当前工作流的可配置参数"""
    if not RUNNINGHUB_API_KEY or not RUNNINGHUB_ID:
        raise HTTPException(status_code=500, detail="RunningHub configuration not complete")

    try:
        params = get_workflow_parameters(RUNNINGHUB_ID, RUNNINGHUB_API_KEY)
        if "error" in params:
            raise HTTPException(status_code=500, detail=params["error"])
        return params
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get workflow parameters: {str(e)}")

@app.post("/api/test-node-parameters")
async def test_node_parameters(test_nodes: List[NodeParameter]):
    """测试特定节点参数是否有效"""
    if not RUNNINGHUB_API_KEY or not RUNNINGHUB_ID:
        raise HTTPException(status_code=500, detail="RunningHub configuration not complete")

    # 创建测试任务
    create_task_url = f"{RUNNINGHUB_API_URL}/task/openapi/create"
    create_headers = {
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Host": "www.runninghub.cn",
        "Connection": "keep-alive"
    }

    node_info_list = []
    for node_param in test_nodes:
        node_info_list.append({
            "nodeId": node_param.node_id,
            "fieldName": node_param.field_name,
            "fieldValue": node_param.field_value
        })

    create_payload = {
        "workflowId": RUNNINGHUB_ID,
        "apiKey": RUNNINGHUB_API_KEY,
        "nodeInfoList": node_info_list
    }

    try:
        create_response = requests.post(create_task_url, headers=create_headers, json=create_payload, timeout=30)
        create_data = create_response.json()

        return {
            "success": create_response.status_code == 200 and create_data.get("code") == 0,
            "response": create_data,
            "tested_nodes": node_info_list
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "tested_nodes": node_info_list
        }

@app.post("/api/generate-logo", response_model=LogoResponse)
async def generate_logo(request: LogoRequest):
    if not RUNNINGHUB_API_KEY:
        raise HTTPException(status_code=500, detail="RunningHub AI API key not configured")

    start_time = datetime.now()

    # 构建增强的提示词
    enhanced_prompt = enhance_prompt(request)

    try:
        print(f"=== DEBUG INFO ===")
        print(f"Original request.prompt: '{request.prompt}'")
        print(f"Company name: {request.company_name}")
        print(f"Industry: {request.industry}")
        print(f"Keywords: {request.keywords}")
        print(f"Color scheme: {request.color_scheme}")
        print(f"Style: {request.style}")
        print(f"Enhanced prompt: {enhanced_prompt}")
        print(f"==================")
        print(f"Generating logo with prompt: {enhanced_prompt}")

        # 创建 RunningHub 任务
        create_task_url = f"{RUNNINGHUB_API_URL}/task/openapi/create"
        create_headers = {
            "Content-Type": "application/json",
            "Accept": "*/*",
            "Host": "www.runninghub.cn",
            "Connection": "keep-alive"
        }

        # 构建节点信息列表，用用户的提示词替换工作流中的文本参数
        node_info_list = []

        # 如果用户提供了自定义参数，优先使用
        if request.custom_parameters:
            for param in request.custom_parameters:
                node_info_list.append({
                    "nodeId": param.node_id,
                    "fieldName": param.field_name,
                    "fieldValue": param.field_value
                })
            print(f"Using {len(node_info_list)} custom parameters from user")
        else:
            # 使用已知的文本输入节点40
            node_info_list.append({
                "nodeId": "40",
                "fieldName": "text",
                "fieldValue": enhanced_prompt
            })
            print(f"Using known text input node 40 with user prompt")

            # 如果用户指定了尺寸，也尝试替换
            if request.width != 1024 or request.height != 1024:
                # 使用已知的尺寸节点5
                node_info_list.append({
                    "nodeId": "5",
                    "fieldName": "width",
                    "fieldValue": request.width
                })
                node_info_list.append({
                    "nodeId": "5",
                    "fieldName": "height",
                    "fieldValue": request.height
                })
                print(f"Using custom dimensions: {request.width}x{request.height}")

            # 可选：获取工作流参数信息作为备用
            try:
                workflow_params = get_workflow_parameters(RUNNINGHUB_ID, RUNNINGHUB_API_KEY)
                if "error" not in workflow_params:
                    print(f"Workflow has {workflow_params.get('total_nodes', 0)} nodes total")
            except Exception as e:
                print(f"Note: Could not get workflow parameters: {e}")

        create_payload = {
            "workflowId": RUNNINGHUB_ID,
            "apiKey": RUNNINGHUB_API_KEY,
            "nodeInfoList": node_info_list
        }

        create_response = requests.post(create_task_url, headers=create_headers, json=create_payload, timeout=60)

        if create_response.status_code != 200:
            error_detail = f"Task creation failed: {create_response.status_code}"
            try:
                error_data = create_response.json()
                if "msg" in error_data:
                    error_detail += f" - {error_data['msg']}"
            except:
                error_detail += f" - {create_response.text}"

            return LogoResponse(
                success=False,
                error=error_detail,
                prompt_used=enhanced_prompt
            )

        create_data = create_response.json()

        if create_data.get("code") != 0 or not create_data.get("data", {}).get("taskId"):
            return LogoResponse(
                success=False,
                error=f"Task creation failed: {create_data.get('msg', 'Unknown error')}",
                prompt_used=enhanced_prompt
            )

        task_id = create_data["data"]["taskId"]
        print(f"Task created successfully. Task ID: {task_id}")

        # 查询任务结果
        query_url = f"{RUNNINGHUB_API_URL}/task/openapi/outputs"
        query_headers = {
            "Content-Type": "application/json",
            "Accept": "*/*",
            "Host": "www.runninghub.cn",
            "Connection": "keep-alive"
        }

        query_payload = {
            "taskId": task_id,
            "apiKey": RUNNINGHUB_API_KEY
        }

        # 轮询查询结果，最多等待10分钟
        max_wait_time = 600  # 10分钟
        poll_interval = 5    # 5秒间隔
        elapsed_time = 0

        while elapsed_time < max_wait_time:
            time.sleep(poll_interval)
            elapsed_time += poll_interval

            query_response = requests.post(query_url, headers=query_headers, json=query_payload, timeout=30)

            if query_response.status_code == 200:
                query_data = query_response.json()

                if query_data.get("msg") == "success" and query_data.get("data"):
                    # 任务完成，获取图像URL
                    image_url = query_data["data"][0]["fileUrl"]
                    print(f"Task completed. Image URL: {image_url}")

                    # 下载图像并保存到本地
                    image_response = requests.get(image_url, timeout=60)
                    if image_response.status_code == 200:
                        # 创建唯一文件名
                        image_id = uuid.uuid4().hex[:12]
                        filename = f"logo_{image_id}_{int(datetime.now().timestamp())}.png"
                        filepath = f"uploads/{filename}"

                        # 保存图像
                        with open(filepath, 'wb') as f:
                            f.write(image_response.content)

                        # 计算生成时间
                        generation_time = (datetime.now() - start_time).total_seconds()

                        print(f"Logo generated successfully: {filename}")

                        return LogoResponse(
                            success=True,
                            image_url=f"/uploads/{filename}",
                            image_id=image_id,
                            prompt_used=enhanced_prompt,
                            style_used=request.style,
                            generation_time=generation_time
                        )
                    else:
                        return LogoResponse(
                            success=False,
                            error="Failed to download generated image",
                            prompt_used=enhanced_prompt
                        )

        # 超时
        return LogoResponse(
            success=False,
            error=f"Task timeout after {max_wait_time} seconds",
            prompt_used=enhanced_prompt
        )

    except requests.exceptions.Timeout:
        return LogoResponse(
            success=False,
            error="Request timeout - AI service took too long to respond",
            prompt_used=enhanced_prompt
        )
    except requests.exceptions.RequestException as e:
        return LogoResponse(
            success=False,
            error=f"Network error: {str(e)}",
            prompt_used=enhanced_prompt
        )
    except Exception as e:
        return LogoResponse(
            success=False,
            error=f"Unexpected error: {str(e)}",
            prompt_used=enhanced_prompt
        )

@app.post("/api/generate-logo-variants", response_model=MultiLogoResponse)
async def generate_logo_variants(request: LogoRequest):
    """生成多个LOGO设计方案"""
    if not RUNNINGHUB_API_KEY:
        raise HTTPException(status_code=500, detail="RunningHub AI API key not configured")

    start_time = datetime.now()
    variants = []

    try:
        # 生成多个不同的提示词
        prompts = generate_multiple_prompts(request)

        # 为每个提示词生成LOGO
        for i, prompt in enumerate(prompts):
            # 创建临时请求对象
            variant_request = LogoRequest(
                prompt=prompt,
                style=request.style,
                width=request.width,
                height=request.height,
                company_name=request.company_name,
                industry=request.industry,
                keywords=request.keywords,
                color_scheme=request.color_scheme
            )

            # 调用单个logo生成函数
            logo_response = await generate_logo(variant_request)

            if logo_response.success and logo_response.image_url:
                # 生成变体描述
                style_names = {
                    'minimalist': '简约风格',
                    'modern': '现代风格',
                    'vintage': '复古风格',
                    'cartoon': '卡通风格',
                    'abstract': '抽象风格',
                    'geometric': '几何风格',
                    'corporate': '企业风格',
                    'handdrawn': '手绘风格'
                }

                variant_descriptions = [
                    f"专业{style_names.get(request.style, '现代')}设计，突出品牌特色",
                    f"创意{style_names.get(request.style, '现代')}方案，独特视觉效果",
                    f"经典{style_names.get(request.style, '现代')}风格，永不过时"
                ]

                variant = LogoVariant(
                    id=logo_response.image_id or uuid.uuid4().hex[:12],
                    image_url=logo_response.image_url,
                    prompt_used=logo_response.prompt_used or prompt,
                    style_name=f"{style_names.get(request.style, '现代')} - 方案{i+1}",
                    description=variant_descriptions[i % len(variant_descriptions)],
                    generation_time=logo_response.generation_time or 0
                )

                variants.append(variant)
                print(f"Variant {i+1} generated successfully")
            else:
                print(f"Failed to generate variant {i+1}: {logo_response.error}")

        total_time = (datetime.now() - start_time).total_seconds()

        if variants:
            return MultiLogoResponse(
                success=True,
                variants=variants,
                total_generation_time=total_time
            )
        else:
            return MultiLogoResponse(
                success=False,
                error="Failed to generate any logo variants"
            )

    except Exception as e:
        return MultiLogoResponse(
            success=False,
            error=f"Error generating logo variants: {str(e)}"
        )

@app.get("/api/styles", response_model=List[StyleInfo])
async def get_styles():
    """获取可用的LOGO风格"""
    styles = [
        StyleInfo(
            id="minimalist",
            name="简约",
            description="简洁现代的设计，注重空白和简单元素",
            example_prompt="clean modern logo, simple geometric shapes"
        ),
        StyleInfo(
            id="vintage",
            name="复古",
            description="经典复古风格，带有怀旧感",
            example_prompt="vintage retro logo, classic typography, aged look"
        ),
        StyleInfo(
            id="modern",
            name="现代",
            description="时尚现代设计，体现科技感",
            example_prompt="modern sleek logo, contemporary design, tech style"
        ),
        StyleInfo(
            id="cartoon",
            name="卡通",
            description="可爱卡通风格，适合儿童品牌",
            example_prompt="playful cartoon logo, fun characters, bright colors"
        ),
        StyleInfo(
            id="abstract",
            name="抽象",
            description="抽象艺术风格，富有创意",
            example_prompt="abstract artistic logo, creative shapes, unique design"
        ),
        StyleInfo(
            id="geometric",
            name="几何",
            description="几何图形设计，结构化布局",
            example_prompt="geometric logo, structured shapes, mathematical design"
        ),
        StyleInfo(
            id="corporate",
            name="企业",
            description="专业企业风格，商务感强",
            example_prompt="professional corporate logo, business identity, formal"
        ),
        StyleInfo(
            id="handdrawn",
            name="手绘",
            description="手绘艺术风格，有机自然",
            example_prompt="hand-drawn logo, artistic sketch, organic style"
        )
    ]
    return styles

@app.get("/api/industries")
async def get_industries():
    """获取支持的行业类型和推荐配置"""
    industries = []

    for industry_id, config in INDUSTRY_TEMPLATES.items():
        industry_info = {
            "id": industry_id,
            "name": {
                "technology": "科技/互联网",
                "food": "餐饮/食品",
                "healthcare": "医疗/健康",
                "education": "教育/培训",
                "finance": "金融/投资",
                "fashion": "时尚/服装",
                "real_estate": "房地产/物业",
                "automotive": "汽车/交通",
                "sports": "体育/运动",
                "beauty": "美容/化妆品",
                "travel": "旅游/出行",
                "entertainment": "娱乐/媒体",
                "consulting": "咨询/服务",
                "retail": "零售/商贸",
                "construction": "建筑/工程"
            }.get(industry_id, industry_id.title()),
            "description": {
                "technology": "适合科技公司、互联网企业、软件开发等",
                "food": "适合餐厅、食品公司、农业企业等",
                "healthcare": "适合医院、诊所、健康产品等",
                "education": "适合学校、培训机构、教育平台等",
                "finance": "适合银行、投资公司、金融服务等",
                "fashion": "适合服装品牌、时尚企业、美容等",
                "real_estate": "适合房地产公司、物业管理、建筑开发等",
                "automotive": "适合汽车制造、汽车服务、交通运输等",
                "sports": "适合体育俱乐部、健身房、运动品牌等",
                "beauty": "适合美容院、化妆品牌、护肤产品等",
                "travel": "适合旅行社、酒店、旅游平台等",
                "entertainment": "适合娱乐公司、媒体机构、游戏开发等",
                "consulting": "适合咨询公司、专业服务、商务顾问等",
                "retail": "适合零售商店、电商平台、商贸公司等",
                "construction": "适合建筑公司、工程承包、装修设计等"
            }.get(industry_id, f"{industry_id} industry"),
            "recommended_styles": config.get('styles', []),
            "recommended_colors": config.get('colors', []),
            "keywords": config.get('keywords', [])[:5]  # 只返回前5个关键词
        }
        industries.append(industry_info)

    return {"industries": industries}

@app.get("/api/recommend")
async def get_recommendations(industry: Optional[str] = None, company_name: Optional[str] = None):
    """基于行业和公司名称获取智能推荐"""
    recommendations = {
        "styles": [],
        "colors": [],
        "keywords": [],
        "tips": []
    }

    if industry and industry in INDUSTRY_TEMPLATES:
        template = INDUSTRY_TEMPLATES[industry]

        recommendations["styles"] = template.get('styles', [])
        recommendations["colors"] = template.get('colors', [])
        recommendations["keywords"] = template.get('keywords', [])[:8]

        # 添加行业特定建议
        industry_tips = {
            "technology": [
                "使用简洁的几何形状体现科技感",
                "蓝色系能传达专业和信任感",
                "避免过于复杂的设计元素"
            ],
            "food": [
                "温暖的颜色能激发食欲",
                "圆润的形状更显亲和力",
                "可以考虑加入食物相关元素"
            ],
            "healthcare": [
                "十字符号是医疗行业的经典元素",
                "蓝色和绿色传达健康和信任",
                "设计要简洁专业，避免花哨"
            ],
            "real_estate": [
                "房屋轮廓是房地产行业的经典元素",
                "蓝色和棕色传达稳定和信任",
                "设计要体现专业和可靠性"
            ],
            "automotive": [
                "动感的线条体现速度和力量",
                "红色和银色是汽车行业的经典色彩",
                "几何形状传达精密和性能"
            ],
            "sports": [
                "动态的形状体现运动和活力",
                "鲜艳的颜色传达能量和激情",
                "可以考虑加入运动相关元素"
            ],
            "beauty": [
                "优雅的曲线体现美感和精致",
                "粉色和金色传达奢华和女性化",
                "设计要精美且有吸引力"
            ],
            "travel": [
                "地球或指南针元素体现全球化",
                "蓝色和绿色传达自由和探索",
                "设计要体现冒险和发现精神"
            ],
            "entertainment": [
                "鲜艳的颜色体现娱乐和活力",
                "创意的形状传达趣味和创新",
                "设计要吸引眼球且富有创意"
            ],
            "consulting": [
                "专业的几何形状体现智慧",
                "深蓝色传达专业和可信度",
                "设计要简洁且具有权威感"
            ],
            "retail": [
                "友好的设计吸引顾客",
                "温暖的颜色营造购物氛围",
                "可以考虑加入购物相关元素"
            ],
            "construction": [
                "坚固的形状体现建筑的稳定性",
                "橙色和黄色是建筑行业的标志色",
                "设计要体现专业和可靠性"
            ]
        }

        recommendations["tips"] = industry_tips.get(industry, [])

    # 基于公司名称的智能分析
    if company_name:
        name_lower = company_name.lower()

        # 名称关键词分析
        if any(word in name_lower for word in ['tech', 'digital', 'ai', 'data', 'cloud']):
            recommendations["keywords"].extend(['innovative', 'digital', 'modern'])
        elif any(word in name_lower for word in ['green', 'eco', 'bio', 'natural']):
            recommendations["keywords"].extend(['natural', 'eco-friendly', 'sustainable'])
        elif any(word in name_lower for word in ['star', 'shine', 'bright', 'light']):
            recommendations["keywords"].extend(['bright', 'shining', 'stellar'])

    # 去重
    recommendations["keywords"] = list(set(recommendations["keywords"]))

    return recommendations

@app.get("/api/download/{filename}")
async def download_file(filename: str):
    """下载生成的LOGO文件"""
    file_path = f"uploads/{filename}"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/octet-stream'
    )

@app.get("/api/logo/{image_id}")
async def get_logo_info(image_id: str):
    """获取LOGO信息"""
    # 这里可以从数据库获取LOGO信息
    # 目前返回基本信息
    return {
        "image_id": image_id,
        "status": "generated",
        "available_formats": ["PNG", "JPG", "SVG"],
        "download_url": f"/api/download/logo_{image_id}"
    }

@app.post("/api/convert/{image_id}")
async def convert_logo_format(image_id: str, target_format: str = "jpg"):
    """转换LOGO格式"""
    # 查找原始文件
    original_files = [f for f in os.listdir("uploads") if f.startswith(f"logo_{image_id}")]
    if not original_files:
        raise HTTPException(status_code=404, detail="Original image not found")

    original_path = f"uploads/{original_files[0]}"

    # 生成新文件名
    new_filename = f"logo_{image_id}.{target_format.lower()}"
    new_path = f"uploads/{new_filename}"

    try:
        # 转换格式
        with Image.open(original_path) as img:
            if target_format.lower() == "jpg":
                # JPG不支持透明度，需要添加白色背景
                if img.mode in ("RGBA", "LA"):
                    background = Image.new("RGB", img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1] if img.mode == "RGBA" else None)
                    img = background
                img.save(new_path, "JPEG", quality=95)
            elif target_format.lower() == "png":
                img.save(new_path, "PNG")
            else:
                raise HTTPException(status_code=400, detail="Unsupported format")

        return {
            "success": True,
            "new_filename": new_filename,
            "download_url": f"/uploads/{new_filename}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Conversion failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
