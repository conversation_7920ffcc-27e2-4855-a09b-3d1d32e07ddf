from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
from dotenv import load_dotenv
import requests
import base64
import io
from PIL import Image

# 加载环境变量
load_dotenv()

app = FastAPI(title="AI Logo Tool API", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Stability AI配置
STABILITY_API_KEY = os.getenv("STABILITY_API_KEY")
STABILITY_API_URL = "https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image"

class LogoRequest(BaseModel):
    prompt: str
    style: str = "minimalist"
    width: int = 1024
    height: int = 1024

class LogoResponse(BaseModel):
    success: bool
    image_url: str = None
    error: str = None

@app.get("/")
async def root():
    return {"message": "AI Logo Tool API", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "api_configured": bool(STABILITY_API_KEY)}

@app.post("/api/generate-logo", response_model=LogoResponse)
async def generate_logo(request: LogoRequest):
    if not STABILITY_API_KEY:
        raise HTTPException(status_code=500, detail="Stability AI API key not configured")
    
    # 构建提示词
    enhanced_prompt = f"{request.prompt}, {request.style} style, logo design, clean, professional, vector style, white background"
    
    # Stability AI API请求
    headers = {
        "Authorization": f"Bearer {STABILITY_API_KEY}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    payload = {
        "text_prompts": [
            {
                "text": enhanced_prompt,
                "weight": 1
            }
        ],
        "cfg_scale": 7,
        "height": request.height,
        "width": request.width,
        "samples": 1,
        "steps": 30,
    }
    
    try:
        response = requests.post(STABILITY_API_URL, headers=headers, json=payload)
        
        if response.status_code != 200:
            return LogoResponse(
                success=False, 
                error=f"API request failed: {response.status_code} - {response.text}"
            )
        
        data = response.json()
        
        if "artifacts" in data and len(data["artifacts"]) > 0:
            # 获取生成的图像
            image_data = data["artifacts"][0]["base64"]
            
            # 保存图像到本地
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
            
            # 创建文件名
            import uuid
            filename = f"logo_{uuid.uuid4().hex[:8]}.png"
            filepath = f"uploads/{filename}"
            
            # 保存图像
            image.save(filepath)
            
            return LogoResponse(
                success=True,
                image_url=f"/uploads/{filename}"
            )
        else:
            return LogoResponse(
                success=False,
                error="No image generated"
            )
            
    except Exception as e:
        return LogoResponse(
            success=False,
            error=f"Error generating logo: {str(e)}"
        )

@app.get("/api/styles")
async def get_styles():
    """获取可用的LOGO风格"""
    styles = [
        {"id": "minimalist", "name": "简约", "description": "简洁现代的设计"},
        {"id": "vintage", "name": "复古", "description": "经典复古风格"},
        {"id": "modern", "name": "现代", "description": "时尚现代设计"},
        {"id": "cartoon", "name": "卡通", "description": "可爱卡通风格"},
        {"id": "abstract", "name": "抽象", "description": "抽象艺术风格"},
        {"id": "geometric", "name": "几何", "description": "几何图形设计"},
        {"id": "corporate", "name": "企业", "description": "专业企业风格"},
        {"id": "handdrawn", "name": "手绘", "description": "手绘艺术风格"}
    ]
    return {"styles": styles}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
