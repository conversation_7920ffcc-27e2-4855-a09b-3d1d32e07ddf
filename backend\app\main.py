from fastapi import Fast<PERSON><PERSON>, HTTPException, File, UploadFile, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
import os
from dotenv import load_dotenv
import requests
import base64
import io
import uuid
import json
from datetime import datetime
from typing import Optional, List
from PIL import Image
import aiofiles

# 加载环境变量
load_dotenv()

app = FastAPI(title="AI Logo Tool API", version="2.0.0", description="AI驱动的智能LOGO设计工具")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:80"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件服务
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Stability AI配置
STABILITY_API_KEY = os.getenv("STABILITY_API_KEY")
STABILITY_API_URL = "https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image"

# 确保上传目录存在
os.makedirs("uploads", exist_ok=True)

# 数据模型
class LogoRequest(BaseModel):
    prompt: str
    style: str = "minimalist"
    width: int = 1024
    height: int = 1024
    company_name: Optional[str] = None
    industry: Optional[str] = None
    keywords: Optional[str] = None
    color_scheme: Optional[str] = None

class LogoResponse(BaseModel):
    success: bool
    image_url: Optional[str] = None
    image_id: Optional[str] = None
    prompt_used: Optional[str] = None
    style_used: Optional[str] = None
    generation_time: Optional[float] = None
    error: Optional[str] = None

class LogoHistory(BaseModel):
    id: str
    prompt: str
    style: str
    image_url: str
    created_at: datetime
    company_name: Optional[str] = None
    industry: Optional[str] = None

class StyleInfo(BaseModel):
    id: str
    name: str
    description: str
    example_prompt: str

# 工具函数
def enhance_prompt(request: LogoRequest) -> str:
    """增强用户提示词，使其更适合LOGO生成"""
    base_prompt = request.prompt

    # 添加公司名称
    if request.company_name:
        base_prompt = f"{request.company_name} {base_prompt}"

    # 添加行业信息
    if request.industry:
        base_prompt += f", {request.industry} industry"

    # 添加关键词
    if request.keywords:
        base_prompt += f", {request.keywords}"

    # 添加风格描述
    style_descriptions = {
        "minimalist": "clean, simple, modern, minimal design",
        "vintage": "retro, classic, vintage style, aged look",
        "modern": "contemporary, sleek, cutting-edge design",
        "cartoon": "playful, fun, cartoon style, colorful",
        "abstract": "abstract, artistic, creative, unique shapes",
        "geometric": "geometric shapes, structured, mathematical",
        "corporate": "professional, business, corporate identity",
        "handdrawn": "hand-drawn, artistic, sketch style, organic"
    }

    style_desc = style_descriptions.get(request.style, "professional")
    base_prompt += f", {style_desc}"

    # 添加LOGO特定要求
    base_prompt += ", logo design, vector style, clean background, high quality, professional"

    # 添加颜色方案
    if request.color_scheme:
        base_prompt += f", {request.color_scheme} color scheme"

    return base_prompt

@app.get("/")
async def root():
    return {
        "message": "AI Logo Tool API",
        "status": "running",
        "version": "2.0.0",
        "features": ["AI Logo Generation", "File Upload/Download", "Style Management"]
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "api_configured": bool(STABILITY_API_KEY),
        "stability_key_present": len(STABILITY_API_KEY) > 0 if STABILITY_API_KEY else False,
        "upload_directory": os.path.exists("uploads"),
        "timestamp": datetime.now().isoformat()
    }

@app.post("/api/generate-logo", response_model=LogoResponse)
async def generate_logo(request: LogoRequest):
    if not STABILITY_API_KEY:
        raise HTTPException(status_code=500, detail="Stability AI API key not configured")

    start_time = datetime.now()

    # 构建增强的提示词
    enhanced_prompt = enhance_prompt(request)

    # Stability AI API请求
    headers = {
        "Authorization": f"Bearer {STABILITY_API_KEY}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    payload = {
        "text_prompts": [
            {
                "text": enhanced_prompt,
                "weight": 1
            }
        ],
        "cfg_scale": 7,
        "height": request.height,
        "width": request.width,
        "samples": 1,
        "steps": 30,
        "style_preset": "digital-art" if request.style in ["modern", "abstract"] else None
    }

    try:
        print(f"Generating logo with prompt: {enhanced_prompt}")
        response = requests.post(STABILITY_API_URL, headers=headers, json=payload, timeout=60)

        if response.status_code != 200:
            error_detail = f"API request failed: {response.status_code}"
            try:
                error_data = response.json()
                if "message" in error_data:
                    error_detail += f" - {error_data['message']}"
            except:
                error_detail += f" - {response.text}"

            return LogoResponse(
                success=False,
                error=error_detail,
                prompt_used=enhanced_prompt
            )

        data = response.json()

        if "artifacts" in data and len(data["artifacts"]) > 0:
            # 获取生成的图像
            image_data = data["artifacts"][0]["base64"]

            # 保存图像到本地
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))

            # 创建唯一文件名
            image_id = uuid.uuid4().hex[:12]
            filename = f"logo_{image_id}_{int(datetime.now().timestamp())}.png"
            filepath = f"uploads/{filename}"

            # 保存图像
            image.save(filepath, "PNG", quality=95)

            # 计算生成时间
            generation_time = (datetime.now() - start_time).total_seconds()

            print(f"Logo generated successfully: {filename}")

            return LogoResponse(
                success=True,
                image_url=f"/uploads/{filename}",
                image_id=image_id,
                prompt_used=enhanced_prompt,
                style_used=request.style,
                generation_time=generation_time
            )
        else:
            return LogoResponse(
                success=False,
                error="No image generated by AI service",
                prompt_used=enhanced_prompt
            )

    except requests.exceptions.Timeout:
        return LogoResponse(
            success=False,
            error="Request timeout - AI service took too long to respond",
            prompt_used=enhanced_prompt
        )
    except requests.exceptions.RequestException as e:
        return LogoResponse(
            success=False,
            error=f"Network error: {str(e)}",
            prompt_used=enhanced_prompt
        )
    except Exception as e:
        return LogoResponse(
            success=False,
            error=f"Unexpected error: {str(e)}",
            prompt_used=enhanced_prompt
        )

@app.get("/api/styles", response_model=List[StyleInfo])
async def get_styles():
    """获取可用的LOGO风格"""
    styles = [
        StyleInfo(
            id="minimalist",
            name="简约",
            description="简洁现代的设计，注重空白和简单元素",
            example_prompt="clean modern logo, simple geometric shapes"
        ),
        StyleInfo(
            id="vintage",
            name="复古",
            description="经典复古风格，带有怀旧感",
            example_prompt="vintage retro logo, classic typography, aged look"
        ),
        StyleInfo(
            id="modern",
            name="现代",
            description="时尚现代设计，体现科技感",
            example_prompt="modern sleek logo, contemporary design, tech style"
        ),
        StyleInfo(
            id="cartoon",
            name="卡通",
            description="可爱卡通风格，适合儿童品牌",
            example_prompt="playful cartoon logo, fun characters, bright colors"
        ),
        StyleInfo(
            id="abstract",
            name="抽象",
            description="抽象艺术风格，富有创意",
            example_prompt="abstract artistic logo, creative shapes, unique design"
        ),
        StyleInfo(
            id="geometric",
            name="几何",
            description="几何图形设计，结构化布局",
            example_prompt="geometric logo, structured shapes, mathematical design"
        ),
        StyleInfo(
            id="corporate",
            name="企业",
            description="专业企业风格，商务感强",
            example_prompt="professional corporate logo, business identity, formal"
        ),
        StyleInfo(
            id="handdrawn",
            name="手绘",
            description="手绘艺术风格，有机自然",
            example_prompt="hand-drawn logo, artistic sketch, organic style"
        )
    ]
    return styles

@app.get("/api/download/{filename}")
async def download_file(filename: str):
    """下载生成的LOGO文件"""
    file_path = f"uploads/{filename}"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/octet-stream'
    )

@app.get("/api/logo/{image_id}")
async def get_logo_info(image_id: str):
    """获取LOGO信息"""
    # 这里可以从数据库获取LOGO信息
    # 目前返回基本信息
    return {
        "image_id": image_id,
        "status": "generated",
        "available_formats": ["PNG", "JPG", "SVG"],
        "download_url": f"/api/download/logo_{image_id}"
    }

@app.post("/api/convert/{image_id}")
async def convert_logo_format(image_id: str, target_format: str = "jpg"):
    """转换LOGO格式"""
    # 查找原始文件
    original_files = [f for f in os.listdir("uploads") if f.startswith(f"logo_{image_id}")]
    if not original_files:
        raise HTTPException(status_code=404, detail="Original image not found")

    original_path = f"uploads/{original_files[0]}"

    # 生成新文件名
    new_filename = f"logo_{image_id}.{target_format.lower()}"
    new_path = f"uploads/{new_filename}"

    try:
        # 转换格式
        with Image.open(original_path) as img:
            if target_format.lower() == "jpg":
                # JPG不支持透明度，需要添加白色背景
                if img.mode in ("RGBA", "LA"):
                    background = Image.new("RGB", img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1] if img.mode == "RGBA" else None)
                    img = background
                img.save(new_path, "JPEG", quality=95)
            elif target_format.lower() == "png":
                img.save(new_path, "PNG")
            else:
                raise HTTPException(status_code=400, detail="Unsupported format")

        return {
            "success": True,
            "new_filename": new_filename,
            "download_url": f"/uploads/{new_filename}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Conversion failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
