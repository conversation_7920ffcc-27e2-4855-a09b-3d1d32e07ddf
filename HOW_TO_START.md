# ⚡ AI Logo Tool 项目启动方法

## 🎯 最简单的启动方式

### 方法1: 一键启动脚本（推荐）
```bash
# 双击运行
start.bat
```

### 方法2: 手动启动
```bash
# 1. 打开PowerShell，进入项目目录
cd C:\Users\<USER>\Desktop\AI_Logo_Tool

# 2. 启动所有服务
docker-compose -f docker-compose.full.yml up -d

# 3. 等待启动完成（约1-2分钟）
```

## 🌐 访问地址

启动成功后访问：
- **🎨 主应用**: http://localhost:3000
- **⚡ 后端API**: http://localhost:8000
- **📚 API文档**: http://localhost:8000/docs

## 📊 检查服务状态

```bash
# 查看所有服务状态
docker-compose -f docker-compose.full.yml ps

# 查看服务日志
docker-compose -f docker-compose.full.yml logs -f
```

## 🛠️ 常用管理命令

```bash
# 启动服务
docker-compose -f docker-compose.full.yml up -d

# 停止服务
docker-compose -f docker-compose.full.yml down

# 重启服务
docker-compose -f docker-compose.full.yml restart

# 重启特定服务
docker-compose -f docker-compose.full.yml restart frontend
docker-compose -f docker-compose.test.yml restart backend
```

## 🔧 故障排除

### 1. Docker未启动
```
错误: The system cannot find the file specified
解决: 启动Docker Desktop，等待完全启动
```

### 2. 端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :8000

# 解决方案：停止占用程序或修改端口
```

### 3. 服务启动失败
```bash
# 查看详细日志
docker-compose -f docker-compose.full.yml logs

# 重新构建并启动
docker-compose -f docker-compose.full.yml up -d --build
```

## 🎉 使用流程

1. **启动项目** - 运行 `start.bat`
2. **打开应用** - 访问 http://localhost:3000
3. **填写信息** - 公司名称、行业类型、设计风格
4. **生成LOGO** - 选择"快速生成"或"多方案"
5. **选择方案** - 从多个设计中选择喜欢的
6. **下载使用** - 下载PNG或转换为其他格式

## 💡 功能亮点

- 🎯 **多方案生成** - 一次生成3个不同设计方案
- 🏭 **行业专业化** - 6个行业的专业设计模板
- 🎨 **智能配色** - 基于行业的颜色推荐
- 📱 **响应式设计** - 支持桌面和移动设备

---

**🚀 现在就开始创建您的专属LOGO吧！**

如有问题，请查看 `START_GUIDE.md` 获取详细说明。
