# 完整版Docker Compose - 包含前端、后端和真实AI功能
services:
  # 前端服务 - Vue3 + TypeScript
  frontend:
    image: node:18-alpine
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8000
    command: >
      sh -c "
        npm install &&
        npm run dev -- --host 0.0.0.0
      "
    depends_on:
      - backend
    networks:
      - ai-logo-network

  # 后端服务 - Python FastAPI
  backend:
    image: python:3.11-slim
    working_dir: /app
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    environment:
      - STABILITY_API_KEY=${STABILITY_API_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    command: >
      bash -c "
        pip install --no-cache-dir -r requirements.txt &&
        python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      "
    networks:
      - ai-logo-network

  # MongoDB 数据库 (可选)
  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=ai_logo_tool
    networks:
      - ai-logo-network

  # Redis 缓存 (可选)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-logo-network

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./uploads:/var/www/uploads
    depends_on:
      - frontend
      - backend
    networks:
      - ai-logo-network

volumes:
  mongodb_data:
  redis_data:

networks:
  ai-logo-network:
    driver: bridge
