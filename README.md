# AI Logo Tool - AI创建LOGO工具

基于AI技术的智能LOGO设计工具，支持文本描述生成、多风格选择、在线编辑和多格式导出。

## 🚀 快速开始

### 前置要求
- Docker 和 Docker Compose
- 在线AI API密钥 (OpenAI 或 Stability AI)

### 安装步骤

1. **克隆项目**
```bash
git clone <your-repo-url>
cd AI_Logo_Tool
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入你的API密钥
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **访问应用**
- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 🏗️ 项目结构

```
AI_Logo_Tool/
├── frontend/           # Vue3 前端应用
│   ├── src/
│   ├── public/
│   ├── Dockerfile
│   └── package.json
├── backend/            # Python FastAPI 后端
│   ├── app/
│   ├── requirements.txt
│   └── Dockerfile
├── nginx/              # Nginx 配置
├── uploads/            # 文件上传目录
├── docker-compose.yml  # Docker 编排文件
├── .env.example        # 环境变量模板
└── README.md
```

## 🔧 开发指南

### 本地开发
```bash
# 启动开发环境
docker-compose up

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 数据库管理
```bash
# 连接MongoDB
docker exec -it ai_logo_tool_mongodb_1 mongosh ai_logo_tool

# 连接Redis
docker exec -it ai_logo_tool_redis_1 redis-cli
```

## 🤖 支持的AI服务

### OpenAI DALL-E
- 高质量图像生成
- 支持详细文本描述
- 需要OpenAI API密钥

### Stability AI
- Stable Diffusion模型
- 多种风格支持
- 需要Stability AI API密钥

## 📋 功能特性

- ✅ 基于文本描述生成LOGO
- ✅ 多种LOGO风格选择
- ✅ 在线编辑和定制
- ✅ 多格式导出 (PNG, JPG, SVG, PDF)
- ✅ 用户管理和历史记录
- ✅ 响应式设计

## 🔒 环境变量说明

| 变量名 | 说明 | 必需 |
|--------|------|------|
| OPENAI_API_KEY | OpenAI API密钥 | 可选 |
| STABILITY_API_KEY | Stability AI API密钥 | 可选 |
| JWT_SECRET_KEY | JWT加密密钥 | 是 |
| MONGODB_URL | MongoDB连接URL | 是 |
| REDIS_URL | Redis连接URL | 是 |

## 📞 支持

如有问题，请查看：
- [API文档](http://localhost:8000/docs)
- [项目Wiki](./docs/)
- [Issue跟踪](./issues/)

## 📄 许可证

MIT License
