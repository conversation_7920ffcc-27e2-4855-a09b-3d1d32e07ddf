from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os

app = FastAPI(title="AI Logo Tool API", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 获取环境变量
STABILITY_API_KEY = os.getenv("STABILITY_API_KEY", "")

class LogoRequest(BaseModel):
    prompt: str
    style: str = "minimalist"
    width: int = 1024
    height: int = 1024

class LogoResponse(BaseModel):
    success: bool
    message: str = ""
    image_url: str = None
    error: str = None

@app.get("/")
async def root():
    return {
        "message": "AI Logo Tool API", 
        "status": "running",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy", 
        "api_configured": bool(STABILITY_API_KEY),
        "stability_key_present": len(STABILITY_API_KEY) > 0,
        "stability_key_length": len(STABILITY_API_KEY) if STABILITY_API_KEY else 0
    }

@app.post("/api/generate-logo", response_model=LogoResponse)
async def generate_logo(request: LogoRequest):
    if not STABILITY_API_KEY:
        return LogoResponse(
            success=False,
            error="Stability AI API key not configured"
        )
    
    # 模拟LOGO生成（暂时返回成功消息）
    return LogoResponse(
        success=True,
        message=f"Logo generation request received: {request.prompt} in {request.style} style",
        image_url="/uploads/sample_logo.png"
    )

@app.get("/api/styles")
async def get_styles():
    """获取可用的LOGO风格"""
    styles = [
        {"id": "minimalist", "name": "简约", "description": "简洁现代的设计"},
        {"id": "vintage", "name": "复古", "description": "经典复古风格"},
        {"id": "modern", "name": "现代", "description": "时尚现代设计"},
        {"id": "cartoon", "name": "卡通", "description": "可爱卡通风格"},
        {"id": "abstract", "name": "抽象", "description": "抽象艺术风格"},
        {"id": "geometric", "name": "几何", "description": "几何图形设计"},
        {"id": "corporate", "name": "企业", "description": "专业企业风格"},
        {"id": "handdrawn", "name": "手绘", "description": "手绘艺术风格"}
    ]
    return {"styles": styles}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
