<template>
  <div id="app">
    <el-container>
      <el-header>
        <h1>🎨 AI Logo Tool</h1>
        <p>AI驱动的智能LOGO设计工具</p>
      </el-header>
      
      <el-main>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card header="创建LOGO">
              <el-form @submit.prevent="generateLogo">
                <el-form-item label="公司/品牌名称">
                  <el-input v-model="logoForm.companyName" placeholder="输入您的公司或品牌名称" />
                </el-form-item>
                
                <el-form-item label="行业类型">
                  <el-select v-model="logoForm.industry" placeholder="选择行业">
                    <el-option label="科技" value="technology" />
                    <el-option label="餐饮" value="food" />
                    <el-option label="时尚" value="fashion" />
                    <el-option label="教育" value="education" />
                    <el-option label="医疗" value="healthcare" />
                    <el-option label="金融" value="finance" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="设计风格">
                  <el-select v-model="logoForm.style" placeholder="选择风格">
                    <el-option label="简约" value="minimalist" />
                    <el-option label="现代" value="modern" />
                    <el-option label="复古" value="vintage" />
                    <el-option label="卡通" value="cartoon" />
                    <el-option label="几何" value="geometric" />
                    <el-option label="企业" value="corporate" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="关键词">
                  <el-input v-model="logoForm.keywords" placeholder="如：创新、专业、活力" />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="generateLogo" :loading="loading">
                    <el-icon><Picture /></el-icon>
                    生成LOGO
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card header="生成结果">
              <div v-if="loading" class="loading-area">
                <el-icon class="is-loading"><Loading /></el-icon>
                <p>AI正在为您生成LOGO...</p>
              </div>
              
              <div v-else-if="generatedImage" class="result-area">
                <img :src="generatedImage" alt="Generated Logo" class="generated-logo" />
                <div class="action-buttons">
                  <el-button type="success">
                    <el-icon><Download /></el-icon>
                    下载PNG
                  </el-button>
                  <el-button type="info">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                </div>
              </div>
              
              <div v-else class="placeholder-area">
                <el-icon><Picture /></el-icon>
                <p>生成的LOGO将在这里显示</p>
              </div>
              
              <div v-if="error" class="error-message">
                <el-alert :title="error" type="error" show-icon />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture, Loading, Download, Edit } from '@element-plus/icons-vue'

const loading = ref(false)
const generatedImage = ref('')
const error = ref('')

const logoForm = reactive({
  companyName: '',
  industry: '',
  style: 'minimalist',
  keywords: ''
})

const generateLogo = async () => {
  if (!logoForm.companyName) {
    ElMessage.warning('请输入公司或品牌名称')
    return
  }
  
  loading.value = true
  error.value = ''
  generatedImage.value = ''
  
  try {
    // 构建提示词
    const prompt = `${logoForm.companyName} logo, ${logoForm.industry} industry, ${logoForm.keywords}`
    
    const response = await fetch('/api/generate-logo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt,
        style: logoForm.style,
        width: 1024,
        height: 1024
      })
    })
    
    const result = await response.json()
    
    if (result.success) {
      generatedImage.value = result.image_url
      ElMessage.success('LOGO生成成功！')
    } else {
      error.value = result.error || '生成失败'
      ElMessage.error('生成失败：' + error.value)
    }
  } catch (err) {
    error.value = '网络错误，请稍后重试'
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
#app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.el-header {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-align: center;
  padding: 20px;
}

.el-header h1 {
  margin: 0;
  font-size: 2.5em;
}

.el-header p {
  margin: 10px 0 0 0;
  opacity: 0.9;
}

.el-main {
  padding: 20px;
}

.loading-area, .placeholder-area {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.loading-area .el-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.placeholder-area .el-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.result-area {
  text-align: center;
}

.generated-logo {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.error-message {
  margin-top: 20px;
}
</style>
