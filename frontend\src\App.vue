<template>
  <div id="app">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="app-header">
        <div class="header-content">
          <div class="logo-section">
            <h1>🎨 AI Logo Tool</h1>
            <p>AI驱动的智能LOGO设计工具</p>
          </div>
          <div class="header-actions">
            <el-button type="text" @click="showHistory = !showHistory">
              <el-icon><Clock /></el-icon>
              历史记录
            </el-button>
            <el-button type="text" @click="showStyles = !showStyles">
              <el-icon><Brush /></el-icon>
              风格预览
            </el-button>
          </div>
        </div>
      </el-header>

      <el-main class="app-main">
        <!-- 风格预览抽屉 -->
        <el-drawer v-model="showStyles" title="LOGO风格预览" size="400px">
          <div class="styles-grid">
            <div
              v-for="style in availableStyles"
              :key="style.id"
              class="style-card"
              :class="{ active: logoForm.style === style.id }"
              @click="selectStyle(style.id)"
            >
              <div class="style-preview">{{ style.name }}</div>
              <div class="style-info">
                <h4>{{ style.name }}</h4>
                <p>{{ style.description }}</p>
              </div>
            </div>
          </div>
        </el-drawer>

        <!-- 历史记录抽屉 -->
        <el-drawer v-model="showHistory" title="生成历史" size="400px">
          <div class="history-list">
            <div v-for="item in generationHistory" :key="item.id" class="history-item">
              <img :src="item.image_url" alt="历史LOGO" class="history-image" />
              <div class="history-info">
                <p class="history-prompt">{{ item.prompt }}</p>
                <p class="history-time">{{ formatTime(item.created_at) }}</p>
                <el-button size="small" @click="downloadImage(item.image_url)">下载</el-button>
              </div>
            </div>
          </div>
        </el-drawer>

        <!-- 多方案选择抽屉 -->
        <el-drawer v-model="showVariants" title="选择您喜欢的设计方案" size="600px">
          <div class="variants-grid">
            <div
              v-for="variant in logoVariants"
              :key="variant.id"
              class="variant-card"
              @click="selectVariant(variant)"
            >
              <div class="variant-image">
                <img :src="variant.image_url" :alt="variant.style_name" />
                <div class="variant-overlay">
                  <el-button type="primary" size="small">选择此方案</el-button>
                </div>
              </div>
              <div class="variant-info">
                <h4>{{ variant.style_name }}</h4>
                <p>{{ variant.description }}</p>
                <div class="variant-meta">
                  <el-tag size="small">{{ variant.generation_time.toFixed(1) }}s</el-tag>
                </div>
              </div>
            </div>
          </div>
          <div v-if="logoVariants.length === 0" class="empty-variants">
            <el-icon><Picture /></el-icon>
            <p>暂无设计方案</p>
          </div>
        </el-drawer>

        <el-row :gutter="24">
          <!-- 左侧：LOGO生成表单 -->
          <el-col :span="12">
            <el-card class="form-card">
              <template #header>
                <div class="card-header">
                  <span>创建您的专属LOGO</span>
                  <el-button type="text" @click="resetForm">
                    <el-icon><Refresh /></el-icon>
                    重置
                  </el-button>
                </div>
              </template>

              <el-form :model="logoForm" label-width="120px" class="logo-form">
                <el-form-item label="公司名称" required>
                  <el-input
                    v-model="logoForm.companyName"
                    placeholder="输入您的公司或品牌名称"
                    :maxlength="50"
                    show-word-limit
                  />
                </el-form-item>

                <el-form-item label="行业类型">
                  <el-select v-model="logoForm.industry" placeholder="选择您的行业" style="width: 100%">
                    <el-option
                      v-for="industry in availableIndustries"
                      :key="industry.id"
                      :label="industry.name"
                      :value="industry.id"
                    >
                      <span>{{ industry.name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ industry.description }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="设计风格">
                  <el-select v-model="logoForm.style" placeholder="选择设计风格" style="width: 100%">
                    <el-option
                      v-for="style in availableStyles"
                      :key="style.id"
                      :label="style.name"
                      :value="style.id"
                    >
                      <span>{{ style.name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ style.description }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="关键词">
                  <el-input
                    v-model="logoForm.keywords"
                    placeholder="如：创新、专业、活力、现代"
                    :maxlength="100"
                    show-word-limit
                  />
                  <div class="form-tip">用逗号分隔多个关键词，这将帮助AI更好地理解您的需求</div>
                </el-form-item>

                <el-form-item label="颜色偏好">
                  <el-select v-model="logoForm.colorScheme" placeholder="选择颜色方案（可选）" style="width: 100%">
                    <el-option label="🔵 蓝色系" value="blue tones">
                      <span style="color: #409eff;">🔵 蓝色系</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">专业、信任</span>
                    </el-option>
                    <el-option label="🔴 红色系" value="red tones">
                      <span style="color: #f56c6c;">🔴 红色系</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">活力、激情</span>
                    </el-option>
                    <el-option label="🟢 绿色系" value="green tones">
                      <span style="color: #67c23a;">🟢 绿色系</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">自然、健康</span>
                    </el-option>
                    <el-option label="🟣 紫色系" value="purple tones">
                      <span style="color: #909399;">🟣 紫色系</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">创意、神秘</span>
                    </el-option>
                    <el-option label="🟠 橙色系" value="orange tones">
                      <span style="color: #e6a23c;">🟠 橙色系</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">温暖、友好</span>
                    </el-option>
                    <el-option label="⚫ 黑白色" value="black and white">
                      <span style="color: #303133;">⚫ 黑白色</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">经典、简约</span>
                    </el-option>
                    <el-option label="🌈 彩色" value="colorful">
                      <span style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">🌈 彩色</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">活泼、多彩</span>
                    </el-option>
                  </el-select>
                  <div class="form-tip">
                    选择颜色偏好将影响AI生成的LOGO色彩倾向
                    <span v-if="logoForm.colorScheme" class="color-preview">
                      当前选择: {{ getColorDisplayName(logoForm.colorScheme) }}
                    </span>
                  </div>
                </el-form-item>

                <el-form-item label="尺寸">
                  <el-radio-group v-model="logoSize">
                    <el-radio label="1024x1024">标准 (1024×1024)</el-radio>
                    <el-radio label="512x512">小尺寸 (512×512)</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item>
                  <el-row :gutter="12">
                    <el-col :span="14">
                      <el-button
                        type="primary"
                        @click="generateLogo"
                        :loading="loading"
                        :disabled="!logoForm.companyName"
                        size="large"
                        style="width: 100%"
                      >
                        <el-icon v-if="!loading"><Picture /></el-icon>
                        {{ loading ? '正在生成中...' : '快速生成' }}
                      </el-button>
                    </el-col>
                    <el-col :span="10">
                      <el-button
                        type="success"
                        @click="generateLogoVariants"
                        :loading="generatingVariants"
                        :disabled="!logoForm.companyName"
                        size="large"
                        style="width: 100%"
                      >
                        <el-icon v-if="!generatingVariants"><Collection /></el-icon>
                        {{ generatingVariants ? '生成中...' : '多方案' }}
                      </el-button>
                    </el-col>
                  </el-row>
                  <div class="form-tip" style="margin-top: 8px;">
                    快速生成：立即生成一个LOGO | 多方案：生成3个不同的设计方案供选择
                  </div>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>

          <!-- 右侧：生成结果 -->
          <el-col :span="12">
            <el-card class="result-card">
              <template #header>
                <div class="card-header">
                  <span>生成结果</span>
                  <div v-if="currentResult">
                    <el-tag size="small">{{ currentResult.style_used }}</el-tag>
                    <span class="generation-time">{{ currentResult.generation_time?.toFixed(1) }}s</span>
                  </div>
                </div>
              </template>

              <!-- 加载状态 -->
              <div v-if="loading" class="loading-area">
                <div class="loading-animation">
                  <el-icon class="is-loading rotating"><Loading /></el-icon>
                </div>
                <h3>AI正在为您创作LOGO...</h3>
                <p>这通常需要10-30秒，请耐心等待</p>
                <el-progress :percentage="loadingProgress" :show-text="false" />
              </div>

              <!-- 生成结果 -->
              <div v-else-if="generatedImage" class="result-area">
                <div class="image-container">
                  <img :src="generatedImage" alt="Generated Logo" class="generated-logo" />
                  <div class="image-overlay">
                    <el-button type="primary" @click="downloadImage(generatedImage)">
                      <el-icon><Download /></el-icon>
                    </el-button>
                    <el-button @click="regenerateLogo">
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </div>
                </div>

                <div class="result-info">
                  <div class="info-item">
                    <strong>使用提示词:</strong>
                    <span class="prompt-text">{{ currentResult?.prompt_used }}</span>
                  </div>
                  <div class="info-item" v-if="logoForm.colorScheme">
                    <strong>颜色偏好:</strong>
                    <el-tag :type="getColorTagType(logoForm.colorScheme)" size="small">
                      {{ getColorDisplayName(logoForm.colorScheme) }}
                    </el-tag>
                  </div>
                  <div class="info-item">
                    <strong>生成时间:</strong>
                    <span>{{ currentResult?.generation_time?.toFixed(1) }}秒</span>
                  </div>
                </div>

                <div class="action-buttons">
                  <el-button type="success" @click="downloadImage(generatedImage)">
                    <el-icon><Download /></el-icon>
                    下载PNG
                  </el-button>
                  <el-button @click="convertFormat('jpg')">
                    <el-icon><Document /></el-icon>
                    转换为JPG
                  </el-button>
                  <el-button @click="regenerateLogo">
                    <el-icon><Refresh /></el-icon>
                    重新生成
                  </el-button>
                </div>
              </div>

              <!-- 占位符 -->
              <div v-else class="placeholder-area">
                <div class="placeholder-icon">
                  <el-icon><Picture /></el-icon>
                </div>
                <h3>准备创建您的专属LOGO</h3>
                <p>填写左侧表单，点击生成按钮开始创作</p>
              </div>

              <!-- 错误信息 -->
              <div v-if="error" class="error-message">
                <el-alert :title="error" type="error" show-icon closable @close="error = ''" />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Picture, Loading, Download, Edit, Clock, Brush,
  Refresh, Document, Collection
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const loadingProgress = ref(0)
const generatedImage = ref('')
const error = ref('')
const showHistory = ref(false)
const showStyles = ref(false)
const availableStyles = ref([])
const availableIndustries = ref([])
const generationHistory = ref([])
const currentResult = ref(null)
const logoVariants = ref([])
const showVariants = ref(false)
const generatingVariants = ref(false)

const logoForm = reactive({
  companyName: '',
  industry: '',
  style: 'minimalist',
  keywords: '',
  colorScheme: ''
})

const logoSize = ref('1024x1024')

// 生命周期
onMounted(async () => {
  await loadStyles()
  await loadIndustries()
  await loadHistory()
})

// 加载可用风格
const loadStyles = async () => {
  try {
    const response = await fetch('/api/styles')
    const data = await response.json()
    availableStyles.value = data
  } catch (err) {
    console.error('Failed to load styles:', err)
  }
}

// 加载可用行业
const loadIndustries = async () => {
  try {
    const response = await fetch('/api/industries')
    const data = await response.json()
    availableIndustries.value = data.industries
  } catch (err) {
    console.error('Failed to load industries:', err)
  }
}

// 加载历史记录
const loadHistory = async () => {
  // 从localStorage加载历史记录
  const saved = localStorage.getItem('logo-history')
  if (saved) {
    generationHistory.value = JSON.parse(saved)
  }
}

// 保存到历史记录
const saveToHistory = (result) => {
  const historyItem = {
    id: Date.now().toString(),
    prompt: result.prompt_used,
    style: result.style_used,
    image_url: result.image_url,
    created_at: new Date().toISOString(),
    company_name: logoForm.companyName,
    industry: logoForm.industry
  }

  generationHistory.value.unshift(historyItem)
  // 只保留最近20条记录
  if (generationHistory.value.length > 20) {
    generationHistory.value = generationHistory.value.slice(0, 20)
  }

  localStorage.setItem('logo-history', JSON.stringify(generationHistory.value))
}

// 选择风格
const selectStyle = (styleId) => {
  logoForm.style = styleId
  showStyles.value = false
  ElMessage.success(`已选择${availableStyles.value.find(s => s.id === styleId)?.name}风格`)
}

// 重置表单
const resetForm = () => {
  Object.assign(logoForm, {
    companyName: '',
    industry: '',
    style: 'minimalist',
    keywords: '',
    colorScheme: ''
  })
  generatedImage.value = ''
  error.value = ''
  currentResult.value = null
}

// 格式化时间
const formatTime = (timeString) => {
  const date = new Date(timeString)
  return date.toLocaleString('zh-CN')
}

// 模拟加载进度
const simulateProgress = () => {
  loadingProgress.value = 0
  const interval = setInterval(() => {
    loadingProgress.value += Math.random() * 15
    if (loadingProgress.value >= 95) {
      loadingProgress.value = 95
      clearInterval(interval)
    }
  }, 1000)

  return interval
}

// 生成LOGO
const generateLogo = async () => {
  if (!logoForm.companyName.trim()) {
    ElMessage.warning('请输入公司或品牌名称')
    return
  }

  loading.value = true
  error.value = ''
  generatedImage.value = ''
  currentResult.value = null

  const progressInterval = simulateProgress()

  try {
    // 构建请求数据
    const [width, height] = logoSize.value.split('x').map(Number)
    const requestData = {
      prompt: `${logoForm.companyName} logo`,
      style: logoForm.style,
      width,
      height,
      company_name: logoForm.companyName,
      industry: logoForm.industry,
      keywords: logoForm.keywords,
      color_scheme: logoForm.colorScheme
    }

    console.log('Generating logo with data:', requestData)

    const response = await fetch('/api/generate-logo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    const result = await response.json()
    console.log('Generation result:', result)

    if (result.success) {
      generatedImage.value = result.image_url
      currentResult.value = result
      saveToHistory(result)
      ElMessage.success('LOGO生成成功！')
      loadingProgress.value = 100
    } else {
      error.value = result.error || '生成失败'
      ElMessage.error('生成失败：' + error.value)
    }
  } catch (err) {
    console.error('Generation error:', err)
    error.value = '网络错误，请稍后重试'
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    clearInterval(progressInterval)
    loading.value = false
    setTimeout(() => {
      loadingProgress.value = 0
    }, 1000)
  }
}

// 重新生成
const regenerateLogo = async () => {
  await generateLogo()
}

// 生成多个方案
const generateLogoVariants = async () => {
  if (!logoForm.companyName.trim()) {
    ElMessage.warning('请输入公司或品牌名称')
    return
  }

  generatingVariants.value = true
  logoVariants.value = []

  try {
    // 构建请求数据
    const [width, height] = logoSize.value.split('x').map(Number)
    const requestData = {
      prompt: `${logoForm.companyName} logo`,
      style: logoForm.style,
      width,
      height,
      company_name: logoForm.companyName,
      industry: logoForm.industry,
      keywords: logoForm.keywords,
      color_scheme: logoForm.colorScheme
    }

    console.log('Generating logo variants with data:', requestData)

    const response = await fetch('/api/generate-logo-variants', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    const result = await response.json()
    console.log('Variants generation result:', result)

    if (result.success && result.variants.length > 0) {
      logoVariants.value = result.variants
      showVariants.value = true
      ElMessage.success(`成功生成${result.variants.length}个设计方案！`)
    } else {
      ElMessage.error('生成方案失败：' + (result.error || '未知错误'))
    }
  } catch (err) {
    console.error('Variants generation error:', err)
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    generatingVariants.value = false
  }
}

// 选择方案
const selectVariant = (variant) => {
  generatedImage.value = variant.image_url
  currentResult.value = {
    image_url: variant.image_url,
    image_id: variant.id,
    prompt_used: variant.prompt_used,
    style_used: variant.style_name,
    generation_time: variant.generation_time
  }
  showVariants.value = false
  ElMessage.success(`已选择${variant.style_name}`)
}

// 下载图片
const downloadImage = async (imageUrl) => {
  try {
    const response = await fetch(imageUrl)
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `logo_${Date.now()}.png`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
    ElMessage.success('下载成功！')
  } catch (err) {
    ElMessage.error('下载失败，请稍后重试')
  }
}

// 转换格式
const convertFormat = async (format) => {
  if (!currentResult.value?.image_id) {
    ElMessage.error('无法转换格式，请重新生成LOGO')
    return
  }

  try {
    const response = await fetch(`/api/convert/${currentResult.value.image_id}?target_format=${format}`, {
      method: 'POST'
    })
    const result = await response.json()

    if (result.success) {
      await downloadImage(result.download_url)
    } else {
      ElMessage.error('格式转换失败')
    }
  } catch (err) {
    ElMessage.error('格式转换失败，请稍后重试')
  }
}

// 获取颜色标签类型
const getColorTagType = (colorScheme) => {
  const colorMap = {
    'blue tones': 'primary',
    'red tones': 'danger',
    'green tones': 'success',
    'purple tones': 'info',
    'orange tones': 'warning',
    'black and white': '',
    'colorful': 'success'
  }
  return colorMap[colorScheme] || ''
}

// 获取颜色显示名称
const getColorDisplayName = (colorScheme) => {
  const nameMap = {
    'blue tones': '🔵 蓝色系',
    'red tones': '🔴 红色系',
    'green tones': '🟢 绿色系',
    'purple tones': '🟣 紫色系',
    'orange tones': '🟠 橙色系',
    'black and white': '⚫ 黑白色',
    'colorful': '🌈 彩色'
  }
  return nameMap[colorScheme] || colorScheme
}
</script>

<style scoped>
#app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* 头部样式 */
.app-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo-section h1 {
  margin: 0;
  font-size: 2.2em;
  color: white;
  font-weight: 600;
}

.logo-section p {
  margin: 5px 0 0 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9em;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.header-actions .el-button {
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 主体样式 */
.app-main {
  padding: 30px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 卡片样式 */
.form-card, .result-card {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.generation-time {
  margin-left: 10px;
  color: #67c23a;
  font-size: 0.9em;
}

/* 表单样式 */
.logo-form {
  padding: 20px 0;
}

.form-tip {
  font-size: 0.8em;
  color: #909399;
  margin-top: 5px;
}

.color-preview {
  display: block;
  margin-top: 5px;
  padding: 4px 8px;
  background: #e1f3ff;
  border-radius: 4px;
  color: #409eff;
  font-weight: 500;
}

/* 加载状态 */
.loading-area {
  text-align: center;
  padding: 60px 20px;
  color: #606266;
}

.loading-animation {
  margin-bottom: 20px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-area .el-icon {
  font-size: 48px;
  color: #409eff;
}

.loading-area h3 {
  margin: 20px 0 10px 0;
  color: #303133;
}

.loading-area p {
  margin-bottom: 30px;
  color: #909399;
}

/* 占位符样式 */
.placeholder-area {
  text-align: center;
  padding: 80px 20px;
  color: #c0c4cc;
}

.placeholder-icon .el-icon {
  font-size: 80px;
  margin-bottom: 20px;
  color: #e4e7ed;
}

.placeholder-area h3 {
  margin: 20px 0 10px 0;
  color: #909399;
}

.placeholder-area p {
  color: #c0c4cc;
}

/* 结果区域 */
.result-area {
  text-align: center;
}

.image-container {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.generated-logo {
  max-width: 100%;
  max-height: 400px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.generated-logo:hover {
  transform: scale(1.02);
}

.image-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.result-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  text-align: left;
  font-size: 0.9em;
  color: #606266;
  border: 1px solid #e4e7ed;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.prompt-text {
  font-family: 'Courier New', monospace;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  word-break: break-all;
  flex: 1;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 风格网格 */
.styles-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  padding: 20px 0;
}

.style-card {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.style-card:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.style-card.active {
  border-color: #409eff;
  background: #e1f3ff;
}

.style-preview {
  font-size: 1.2em;
  font-weight: 600;
  margin-bottom: 8px;
  color: #303133;
}

.style-info h4 {
  margin: 0 0 5px 0;
  color: #409eff;
}

.style-info p {
  margin: 0;
  font-size: 0.9em;
  color: #606266;
}

/* 历史记录 */
.history-list {
  padding: 20px 0;
}

.history-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.3s ease;
}

.history-item:hover {
  background: #f8f9fa;
}

.history-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-info {
  flex: 1;
}

.history-prompt {
  margin: 0 0 5px 0;
  font-weight: 500;
  color: #303133;
  font-size: 0.9em;
}

.history-time {
  margin: 0 0 10px 0;
  font-size: 0.8em;
  color: #909399;
}

/* 方案选择 */
.variants-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  padding: 20px 0;
}

.variant-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.variant-card:hover {
  border-color: #409eff;
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.variant-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.variant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.variant-card:hover .variant-image img {
  transform: scale(1.05);
}

.variant-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.variant-card:hover .variant-overlay {
  opacity: 1;
}

.variant-info {
  padding: 15px;
}

.variant-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 1.1em;
  font-weight: 600;
}

.variant-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 0.9em;
  line-height: 1.4;
}

.variant-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-variants {
  text-align: center;
  padding: 60px 20px;
  color: #c0c4cc;
}

.empty-variants .el-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

/* 错误信息 */
.error-message {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
  }

  .app-main {
    padding: 20px 10px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .generated-logo {
    max-height: 300px;
  }
}
</style>
