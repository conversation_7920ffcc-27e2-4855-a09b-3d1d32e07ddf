import requests
import json
import os

RUNNINGHUB_API_KEY = os.getenv('RUNNINGHUB_API_KEY', '906e221ce02f477c8926205f44b0b5b9')
RUNNINGHUB_ID = os.getenv('RUNNINGHUB_ID', '1931900822227976194')
RUNNINGHUB_API_URL = 'https://www.runninghub.cn/api'

print(f'工作流ID: {RUNNINGHUB_ID}')
print(f'API密钥: {RUNNINGHUB_API_KEY[:10] if RUNNINGHUB_API_KEY else None}...')

# 测试常见的文本输入节点
possible_nodes = [
    {'node_id': '6', 'field_name': 'text'},
    {'node_id': '7', 'field_name': 'text'}, 
    {'node_id': '40', 'field_name': 'text'},
    {'node_id': '6', 'field_name': 'prompt'},
    {'node_id': '7', 'field_name': 'prompt'},
]

for node_config in possible_nodes:
    print(f'\n测试节点 {node_config["node_id"]} 字段 {node_config["field_name"]}...')
    
    create_task_url = f'{RUNNINGHUB_API_URL}/task/openapi/create'
    create_headers = {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'www.runninghub.cn',
        'Connection': 'keep-alive'
    }
    
    node_info_list = [{
        'nodeId': node_config['node_id'],
        'fieldName': node_config['field_name'],
        'fieldValue': 'test logo design'
    }]
    
    create_payload = {
        'workflowId': RUNNINGHUB_ID,
        'apiKey': RUNNINGHUB_API_KEY,
        'nodeInfoList': node_info_list
    }
    
    try:
        create_response = requests.post(create_task_url, headers=create_headers, json=create_payload, timeout=30)
        create_data = create_response.json()
        
        print(f'状态码: {create_response.status_code}')
        print(f'响应: {create_data}')
        
        if create_response.status_code == 200 and create_data.get('code') == 0:
            print(f'✅ 成功！节点 {node_config["node_id"]} 字段 {node_config["field_name"]} 有效')
            break
        else:
            print(f'❌ 失败: {create_data.get("msg", "未知错误")}')
            
    except Exception as e:
        print(f'❌ 异常: {str(e)}')
