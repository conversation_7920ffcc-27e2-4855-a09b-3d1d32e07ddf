# 简化版Docker Compose - 仅启动核心服务
services:
  # 后端服务 - Python FastAPI
  backend:
    image: python:3.11-slim
    working_dir: /app
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    environment:
      - STABILITY_API_KEY=${STABILITY_API_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    command: >
      bash -c "
        apt-get update && apt-get install -y gcc g++ &&
        pip install --no-cache-dir -r requirements.txt &&
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      "
    networks:
      - ai-logo-network

  # MongoDB 数据库
  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=ai_logo_tool
    networks:
      - ai-logo-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-logo-network

volumes:
  mongodb_data:
  redis_data:

networks:
  ai-logo-network:
    driver: bridge
