# 🚀 AI Logo Tool 项目启动指南

## 📋 前置要求

### 必需软件
- ✅ **Docker Desktop** - 已安装
- ✅ **Stability AI API密钥** - 已配置

### 检查Docker状态
```bash
# 确保Docker Desktop正在运行
docker --version
docker info
```

## 🎯 快速启动（推荐）

### 方式1: 一键启动脚本
```bash
# Windows PowerShell
.\start.bat

# 或者使用bash脚本
bash start.sh
```

### 方式2: 手动启动
```bash
# 1. 进入项目目录
cd C:\Users\<USER>\Desktop\AI_Logo_Tool

# 2. 启动完整服务（前端+后端）
docker-compose -f docker-compose.full.yml up -d

# 3. 查看服务状态
docker-compose -f docker-compose.full.yml ps
```

### 方式3: 分步启动
```bash
# 先启动后端
docker-compose -f docker-compose.test.yml up -d

# 等待后端启动完成，然后启动前端
docker-compose -f docker-compose.full.yml up frontend -d
```

## 🌐 访问地址

启动成功后，您可以访问：

- **🎨 前端应用**: http://localhost:3000
- **⚡ 后端API**: http://localhost:8000  
- **📚 API文档**: http://localhost:8000/docs
- **💾 健康检查**: http://localhost:8000/health

## 📊 服务状态检查

### 检查所有服务
```bash
docker-compose -f docker-compose.full.yml ps
```

### 查看服务日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.full.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.full.yml logs -f frontend
docker-compose -f docker-compose.full.yml logs -f backend
```

### 测试API是否正常
```bash
# PowerShell测试
Invoke-WebRequest -Uri "http://localhost:8000/health" -UseBasicParsing

# 或使用curl
curl http://localhost:8000/health
```

## 🛠️ 常用管理命令

### 启动服务
```bash
# 启动所有服务
docker-compose -f docker-compose.full.yml up -d

# 启动特定服务
docker-compose -f docker-compose.full.yml up frontend -d
docker-compose -f docker-compose.test.yml up backend -d
```

### 停止服务
```bash
# 停止所有服务
docker-compose -f docker-compose.full.yml down

# 停止特定服务
docker-compose -f docker-compose.full.yml stop frontend
```

### 重启服务
```bash
# 重启所有服务
docker-compose -f docker-compose.full.yml restart

# 重启特定服务
docker-compose -f docker-compose.full.yml restart frontend
docker-compose -f docker-compose.test.yml restart backend
```

### 查看服务状态
```bash
# 查看运行状态
docker-compose -f docker-compose.full.yml ps

# 查看资源使用
docker stats
```

## 🔧 故障排除

### 问题1: Docker未启动
```
错误: The system cannot find the file specified
解决: 启动Docker Desktop应用程序，等待完全启动
```

### 问题2: 端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :8000

# 解决方案：修改docker-compose.yml中的端口映射
# 或者停止占用端口的程序
```

### 问题3: 前端无法访问
```bash
# 检查前端服务状态
docker-compose -f docker-compose.full.yml logs frontend

# 重启前端服务
docker-compose -f docker-compose.full.yml restart frontend
```

### 问题4: 后端API错误
```bash
# 检查后端日志
docker-compose -f docker-compose.test.yml logs backend

# 检查环境变量
cat .env

# 重启后端服务
docker-compose -f docker-compose.test.yml restart backend
```

### 问题5: AI生成失败
```
可能原因：
1. Stability AI API密钥错误
2. 网络连接问题
3. API配额用完

解决方案：
1. 检查.env文件中的STABILITY_API_KEY
2. 检查网络连接
3. 查看Stability AI账户余额
```

## 📁 项目文件结构

```
AI_Logo_Tool/
├── 🐳 docker-compose.full.yml    # 完整服务配置
├── 🐳 docker-compose.test.yml    # 后端测试配置
├── ⚙️ .env                       # 环境变量（已配置）
├── 📁 frontend/                  # Vue3前端应用
├── 📁 backend/                   # FastAPI后端
├── 📁 uploads/                   # 生成的图片存储
├── 📁 nginx/                     # Nginx配置
├── 🚀 start.sh                   # 启动脚本
├── 📖 README.md                  # 项目说明
└── 📋 START_GUIDE.md             # 本启动指南
```

## 🎯 推荐启动流程

### 第一次启动
1. **确认Docker运行**: 检查Docker Desktop状态
2. **启动后端**: `docker-compose -f docker-compose.test.yml up -d`
3. **等待后端就绪**: 访问 http://localhost:8000/health 确认
4. **启动前端**: `docker-compose -f docker-compose.full.yml up frontend -d`
5. **访问应用**: 打开 http://localhost:3000

### 日常使用
```bash
# 一键启动所有服务
docker-compose -f docker-compose.full.yml up -d

# 访问应用
open http://localhost:3000
```

### 开发调试
```bash
# 查看实时日志
docker-compose -f docker-compose.full.yml logs -f

# 重启特定服务
docker-compose -f docker-compose.full.yml restart frontend
```

## 🎉 启动成功标志

当您看到以下内容时，说明启动成功：

1. **Docker容器状态**: 所有容器显示"Up"状态
2. **健康检查**: http://localhost:8000/health 返回正常
3. **前端访问**: http://localhost:3000 正常加载
4. **API文档**: http://localhost:8000/docs 可以访问

## 💡 使用提示

### 功能体验
1. **快速生成**: 填写公司名称，点击"快速生成"
2. **多方案生成**: 点击"多方案"按钮，获得3个设计方案
3. **行业专业化**: 选择不同行业，体验专业化设计
4. **颜色偏好**: 选择颜色方案，看到实时预览效果

### 性能优化
- 首次启动可能需要下载Docker镜像，请耐心等待
- 前端热重载功能，修改代码后自动刷新
- 后端支持热重载，修改Python代码后自动重启

---

**🚀 现在您可以开始使用AI Logo Tool了！**

如有问题，请查看故障排除部分或检查服务日志。
