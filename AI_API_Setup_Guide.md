# AI API 配置指南

## 🤖 支持的AI服务

### 1. OpenAI DALL-E API

**获取API密钥：**
1. 访问 [OpenAI Platform](https://platform.openai.com/)
2. 注册/登录账户
3. 进入 API Keys 页面
4. 点击 "Create new secret key"
5. 复制生成的API密钥

**特点：**
- ✅ 高质量图像生成
- ✅ 理解复杂文本描述
- ✅ 多种尺寸支持
- 💰 按使用量付费

**定价：** 
- DALL-E 3: $0.040/图像 (1024×1024)
- DALL-E 2: $0.020/图像 (1024×1024)

### 2. Stability AI API

**获取API密钥：**
1. 访问 [Stability AI Platform](https://platform.stability.ai/)
2. 注册账户
3. 进入 API Keys 页面
4. 生成新的API密钥
5. 复制API密钥

**特点：**
- ✅ Stable Diffusion模型
- ✅ 多种风格和参数控制
- ✅ 相对便宜
- ✅ 开源友好

**定价：**
- 约 $0.002-0.01/图像（根据参数）

### 3. 其他可选服务

**Midjourney API (第三方):**
- 需要通过第三方服务商
- 图像质量极高
- 成本较高

**Replicate API:**
- 多种开源模型
- 灵活的定价
- 支持自定义模型

## 🔧 配置步骤

### 1. 复制环境变量文件
```bash
cp .env.example .env
```

### 2. 编辑 .env 文件
```bash
# 至少配置一个AI服务
OPENAI_API_KEY=sk-your-openai-key-here
STABILITY_API_KEY=sk-your-stability-key-here

# 必需配置
JWT_SECRET_KEY=your-random-secret-key-here
```

### 3. 生成JWT密钥
```bash
# 使用Python生成随机密钥
python -c "import secrets; print(secrets.token_urlsafe(32))"

# 或使用在线工具生成
```

## 💡 使用建议

### 成本优化
1. **开发阶段**: 使用Stability AI (成本更低)
2. **生产环境**: 根据质量需求选择
3. **混合使用**: 不同场景使用不同服务

### 质量对比
| 服务 | 质量 | 速度 | 成本 | 控制性 |
|------|------|------|------|--------|
| DALL-E 3 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ |
| DALL-E 2 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| Stability AI | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

### 提示词优化
```
好的LOGO提示词示例：
"Modern minimalist logo for tech startup, clean geometric shapes, blue and white color scheme, professional, vector style"

避免的提示词：
"Make me a logo" (太模糊)
"Colorful busy design with lots of elements" (太复杂)
```

## 🔒 安全注意事项

1. **API密钥安全**
   - 不要提交到版本控制
   - 定期轮换密钥
   - 使用环境变量

2. **使用限制**
   - 设置API调用限制
   - 监控使用量
   - 实现缓存机制

3. **内容过滤**
   - 实现内容审核
   - 遵守服务条款
   - 避免版权问题

## 🚀 快速测试

配置完成后，可以通过以下方式测试：

```bash
# 启动服务
docker-compose up -d

# 访问API文档
open http://localhost:8000/docs

# 测试AI生成接口
curl -X POST "http://localhost:8000/api/generate-logo" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "modern tech logo", "style": "minimalist"}'
```
