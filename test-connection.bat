@echo off
chcp 65001 >nul
echo.
echo 🔍 前后端连接测试
echo ================================
echo.

echo 📊 检查服务状态...
docker-compose -f docker-compose.full.yml ps
echo.

echo 🔍 测试后端健康检查...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 后端API正常 - 状态码:' $response.StatusCode -ForegroundColor Green; Write-Host '响应内容:' $response.Content } catch { Write-Host '❌ 后端API无法访问:' $_.Exception.Message -ForegroundColor Red }"
echo.

echo 🔍 测试前端访问...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 前端正常 - 状态码:' $response.StatusCode -ForegroundColor Green } catch { Write-Host '❌ 前端无法访问:' $_.Exception.Message -ForegroundColor Red }"
echo.

echo 🔍 测试前端API代理...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/api/health' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 前端API代理正常 - 状态码:' $response.StatusCode -ForegroundColor Green } catch { Write-Host '❌ 前端API代理失败:' $_.Exception.Message -ForegroundColor Red }"
echo.

echo 🔍 测试LOGO生成API...
powershell -Command "try { $body = '{\"prompt\": \"test logo\", \"style\": \"modern\", \"company_name\": \"TestCorp\", \"industry\": \"technology\", \"width\": 1024, \"height\": 1024}'; $response = Invoke-WebRequest -Uri 'http://localhost:8000/api/generate-logo' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 30; Write-Host '✅ LOGO生成API正常 - 状态码:' $response.StatusCode -ForegroundColor Green } catch { Write-Host '❌ LOGO生成API失败:' $_.Exception.Message -ForegroundColor Red }"
echo.

echo 📋 查看前端日志（最后10行）...
docker-compose -f docker-compose.full.yml logs --tail=10 frontend
echo.

echo 📋 查看后端日志（最后10行）...
docker-compose -f docker-compose.test.yml logs --tail=10 backend
echo.

echo 🔧 诊断完成！
echo.
pause
