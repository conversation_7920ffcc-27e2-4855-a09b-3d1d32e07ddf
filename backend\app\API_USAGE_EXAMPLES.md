# RunningHub API 使用示例

## 1. 获取工作流参数

获取当前工作流的可配置参数：

```bash
curl -X GET "http://localhost:8000/api/workflow-parameters"
```

**响应示例：**
```json
{
  "workflow_id": "1931900822227976194",
  "parameters": {
    "17_steps": {
      "type": "number",
      "field_name": "steps",
      "current_value": 30,
      "class_type": "BasicScheduler",
      "description": "steps parameter for BasicScheduler"
    },
    "17_denoise": {
      "type": "number", 
      "field_name": "denoise",
      "current_value": 1,
      "class_type": "BasicScheduler",
      "description": "denoise parameter for BasicScheduler"
    },
    "5_width": {
      "type": "number",
      "field_name": "width", 
      "current_value": 1024,
      "class_type": "EmptyLatentImage",
      "description": "width parameter for EmptyLatentImage"
    },
    "5_height": {
      "type": "number",
      "field_name": "height",
      "current_value": 1536,
      "class_type": "EmptyLatentImage", 
      "description": "height parameter for EmptyLatentImage"
    },
    "5_batch_size": {
      "type": "number",
      "field_name": "batch_size",
      "current_value": 4,
      "class_type": "EmptyLatentImage",
      "description": "batch_size parameter for EmptyLatentImage"
    }
  },
  "total_nodes": 13
}
```

## 2. 基本 Logo 生成

使用默认参数生成 logo：

```bash
curl -X POST "http://localhost:8000/api/generate-logo" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "modern tech company logo",
    "style": "minimalist",
    "company_name": "TechCorp"
  }'
```

## 3. 使用自定义节点参数生成 Logo

如果您知道特定的节点ID和参数，可以直接指定：

```bash
curl -X POST "http://localhost:8000/api/generate-logo" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "modern tech company logo",
    "style": "minimalist", 
    "company_name": "TechCorp",
    "custom_parameters": [
      {
        "node_id": "5",
        "field_name": "width",
        "field_value": "512"
      },
      {
        "node_id": "5", 
        "field_name": "height",
        "field_value": "512"
      },
      {
        "node_id": "17",
        "field_name": "steps", 
        "field_value": "20"
      }
    ]
  }'
```

## 4. 测试节点参数

测试特定节点参数是否有效：

```bash
curl -X POST "http://localhost:8000/api/test-node-parameters" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "node_id": "5",
      "field_name": "width", 
      "field_value": "512"
    }
  ]'
```

## 5. 可用的节点参数

根据工作流分析和测试，以下参数可以配置：

### 文本输入 (节点40 - CLIP文本编码)
- `text`: 文本提示词 (系统会自动使用此节点)

### 图像尺寸 (节点5 - EmptyLatentImage)
- `width`: 图像宽度 (默认: 1024)
- `height`: 图像高度 (默认: 1536)
- `batch_size`: 批次大小 (默认: 4)

### 生成参数 (节点17 - BasicScheduler)
- `steps`: 生成步数 (默认: 30)
- `denoise`: 去噪强度 (默认: 1.0)

## 6. 注意事项

1. **文本提示词**: 当前工作流可能没有可直接配置的文本输入节点，系统会使用内置的提示词增强功能
2. **节点ID**: 节点ID必须与工作流中的实际节点匹配，否则会返回 `APIKEY_INVALID_NODE_INFO` 错误
3. **参数类型**: 数值参数需要作为字符串传递给 `field_value`
4. **生成时间**: 修改步数等参数会影响生成时间

## 7. 错误处理

常见错误及解决方案：

- `APIKEY_INVALID_NODE_INFO`: 节点ID或字段名不正确
- `Task timeout`: 生成时间过长，可以尝试减少步数
- `API key not configured`: 检查环境变量配置

## 8. 完整的 Python 示例

```python
import requests

# 基本生成
response = requests.post("http://localhost:8000/api/generate-logo", json={
    "prompt": "elegant restaurant logo",
    "style": "vintage",
    "company_name": "Bella Vista"
})

# 自定义参数生成
response = requests.post("http://localhost:8000/api/generate-logo", json={
    "prompt": "tech startup logo", 
    "style": "modern",
    "custom_parameters": [
        {"node_id": "5", "field_name": "width", "field_value": "768"},
        {"node_id": "5", "field_name": "height", "field_value": "768"},
        {"node_id": "17", "field_name": "steps", "field_value": "25"}
    ]
})

print(response.json())
```
