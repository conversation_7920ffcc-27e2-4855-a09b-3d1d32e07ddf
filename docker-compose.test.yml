# 完整版Docker Compose - 包含真实AI功能
services:
  # 后端服务 - 完整版本
  backend:
    image: python:3.11-slim
    working_dir: /app
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    environment:
      - STABILITY_API_KEY=${STABILITY_API_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    command: >
      bash -c "
        apt-get update && apt-get install -y gcc g++ &&
        pip install --no-cache-dir -r requirements.txt &&
        python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      "
    networks:
      - ai-logo-network

networks:
  ai-logo-network:
    driver: bridge
