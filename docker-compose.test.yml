# 测试版Docker Compose - 仅启动后端API测试
services:
  # 后端服务 - 简化版本
  backend:
    image: python:3.11-slim
    working_dir: /app
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    environment:
      - STABILITY_API_KEY=${STABILITY_API_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    command: >
      bash -c "
        pip install --no-cache-dir -r simple_requirements.txt &&
        python -m uvicorn app.simple_main:app --host 0.0.0.0 --port 8000 --reload
      "
    networks:
      - ai-logo-network

networks:
  ai-logo-network:
    driver: bridge
