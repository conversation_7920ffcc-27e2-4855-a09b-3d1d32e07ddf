#!/usr/bin/env python3
"""
测试脚本：系统性地测试所有可能的节点ID和字段名组合
"""

import requests
import json

def test_node_parameter(node_id, field_name, field_value="test"):
    """测试单个节点参数"""
    url = "http://localhost:8000/api/test-node-parameters"
    payload = [{
        "node_id": str(node_id),
        "field_name": field_name,
        "field_value": field_value
    }]
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        result = response.json()
        return result.get("success", False), result.get("response", {})
    except Exception as e:
        return False, {"error": str(e)}

def main():
    """主测试函数"""
    print("开始测试节点参数...")
    
    # 测试的字段名列表
    field_names = ["text", "prompt", "positive", "negative"]
    
    # 测试节点ID 1-13
    successful_nodes = []
    
    for node_id in range(1, 14):
        for field_name in field_names:
            print(f"测试节点 {node_id}, 字段 {field_name}...")
            success, response = test_node_parameter(node_id, field_name)
            
            if success:
                print(f"✅ 成功: 节点 {node_id}, 字段 {field_name}")
                successful_nodes.append({
                    "node_id": node_id,
                    "field_name": field_name,
                    "response": response
                })
            else:
                error_msg = response.get("msg", "Unknown error")
                if error_msg != "APIKEY_INVALID_NODE_INFO":
                    print(f"❌ 错误: 节点 {node_id}, 字段 {field_name} - {error_msg}")
    
    print("\n=== 测试结果 ===")
    if successful_nodes:
        print("找到以下有效的节点参数:")
        for node in successful_nodes:
            print(f"- 节点 {node['node_id']}, 字段 {node['field_name']}")
    else:
        print("没有找到有效的文本节点参数")
    
    # 保存结果到文件
    with open("node_test_results.json", "w", encoding="utf-8") as f:
        json.dump(successful_nodes, f, indent=2, ensure_ascii=False)
    
    print(f"\n结果已保存到 node_test_results.json")

if __name__ == "__main__":
    main()
