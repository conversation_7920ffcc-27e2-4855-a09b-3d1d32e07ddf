#!/usr/bin/env python3
"""
测试工作流节点，找到正确的文本输入节点
"""
import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

RUNNINGHUB_API_KEY = os.getenv("RUNNINGHUB_API_KEY")
RUNNINGHUB_ID = os.getenv("RUNNINGHUB_ID")
RUNNINGHUB_API_URL = "https://www.runninghub.cn/api"

def test_text_nodes():
    """测试常见的文本输入节点ID"""
    
    # 常见的文本输入节点ID
    possible_text_nodes = [
        {"node_id": "6", "field_name": "text"},
        {"node_id": "7", "field_name": "text"}, 
        {"node_id": "40", "field_name": "text"},
        {"node_id": "41", "field_name": "text"},
        {"node_id": "1", "field_name": "text"},
        {"node_id": "2", "field_name": "text"},
        {"node_id": "3", "field_name": "text"},
        {"node_id": "4", "field_name": "text"},
        {"node_id": "8", "field_name": "text"},
        {"node_id": "9", "field_name": "text"},
        {"node_id": "10", "field_name": "text"},
        # 尝试不同的字段名
        {"node_id": "6", "field_name": "prompt"},
        {"node_id": "7", "field_name": "prompt"},
        {"node_id": "40", "field_name": "prompt"},
        {"node_id": "6", "field_name": "positive"},
        {"node_id": "7", "field_name": "positive"},
    ]
    
    test_prompt = "test logo design"
    
    for node_config in possible_text_nodes:
        print(f"\n测试节点 {node_config['node_id']} 字段 {node_config['field_name']}...")
        
        # 创建测试任务
        create_task_url = f"{RUNNINGHUB_API_URL}/task/openapi/create"
        create_headers = {
            "Content-Type": "application/json",
            "Accept": "*/*",
            "Host": "www.runninghub.cn",
            "Connection": "keep-alive"
        }
        
        node_info_list = [{
            "nodeId": node_config["node_id"],
            "fieldName": node_config["field_name"],
            "fieldValue": test_prompt
        }]
        
        create_payload = {
            "workflowId": RUNNINGHUB_ID,
            "apiKey": RUNNINGHUB_API_KEY,
            "nodeInfoList": node_info_list
        }
        
        try:
            create_response = requests.post(create_task_url, headers=create_headers, json=create_payload, timeout=30)
            create_data = create_response.json()
            
            print(f"状态码: {create_response.status_code}")
            print(f"响应: {json.dumps(create_data, indent=2, ensure_ascii=False)}")
            
            if create_response.status_code == 200 and create_data.get("code") == 0:
                print(f"✅ 成功！节点 {node_config['node_id']} 字段 {node_config['field_name']} 有效")
                task_id = create_data.get("data", {}).get("taskId")
                if task_id:
                    print(f"任务ID: {task_id}")
                return node_config
            else:
                print(f"❌ 失败: {create_data.get('msg', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
    
    print("\n❌ 没有找到有效的文本输入节点")
    return None

if __name__ == "__main__":
    if not RUNNINGHUB_API_KEY or not RUNNINGHUB_ID:
        print("❌ 请确保设置了 RUNNINGHUB_API_KEY 和 RUNNINGHUB_ID 环境变量")
        exit(1)
    
    print(f"工作流ID: {RUNNINGHUB_ID}")
    print(f"API密钥: {RUNNINGHUB_API_KEY[:10]}...")
    
    result = test_text_nodes()
    if result:
        print(f"\n🎉 找到正确的文本输入节点:")
        print(f"节点ID: {result['node_id']}")
        print(f"字段名: {result['field_name']}")
