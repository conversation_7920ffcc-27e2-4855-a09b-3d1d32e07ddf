#!/usr/bin/env python3
import sys
import os
sys.path.append('/app')

# 导入必要的模块
from pydantic import BaseModel
from typing import Optional

# 复制模型定义
class LogoRequest(BaseModel):
    prompt: str
    style: str = "minimalist"
    width: int = 1024
    height: int = 1024
    company_name: Optional[str] = None
    industry: Optional[str] = None
    keywords: Optional[str] = None
    color_scheme: Optional[str] = None
    custom_parameters: Optional[list] = None

# 复制行业模板
INDUSTRY_TEMPLATES = {
    'technology': {
        'keywords': ['modern', 'digital', 'innovation', 'tech', 'smart', 'future'],
        'colors': ['blue tones', 'gray tones', 'blue and silver'],
        'styles': ['minimalist', 'geometric', 'modern'],
        'shapes': 'geometric shapes, clean lines, angular design'
    },
    'food': {
        'keywords': ['delicious', 'fresh', 'organic', 'tasty', 'natural', 'healthy'],
        'colors': ['red tones', 'orange tones', 'warm colors'],
        'styles': ['vintage', 'handdrawn', 'modern', 'cartoon'],
        'shapes': 'rounded shapes, organic forms, flowing lines'
    }
}

# 复制风格变体
STYLE_VARIANTS = {
    'modern': [
        'contemporary, sleek, cutting-edge design',
        'modern minimalist, clean aesthetic',
        'futuristic, innovative styling'
    ]
}

def enhance_prompt(request: LogoRequest, variant_index: int = 0) -> str:
    """基于用户参数构建全新的LOGO生成提示词"""
    
    # 从用户参数重新构建提示词，忽略原有的prompt字段
    prompt_parts = []
    
    # 1. 公司名称 + logo（核心部分）
    if request.company_name:
        prompt_parts.append(f"{request.company_name} logo")
    else:
        prompt_parts.append("logo design")

    # 2. 获取行业模板
    industry_template = INDUSTRY_TEMPLATES.get(request.industry or "", {})

    # 3. 添加行业信息
    if request.industry:
        prompt_parts.append(f"{request.industry} industry")

    # 4. 收集所有关键词（避免重复）
    all_keywords = []
    
    # 添加行业特定关键词
    if request.industry and industry_template.get('keywords'):
        industry_keywords = industry_template['keywords'][:3]  # 取前3个关键词
        all_keywords.extend(industry_keywords)

    # 添加用户关键词
    if request.keywords:
        # 分割用户关键词并清理
        user_keywords = [kw.strip() for kw in request.keywords.split(',') if kw.strip()]
        all_keywords.extend(user_keywords)
    
    # 去重并保持顺序
    unique_keywords = []
    seen = set()
    for keyword in all_keywords:
        keyword_lower = keyword.lower()
        if keyword_lower not in seen:
            unique_keywords.append(keyword)
            seen.add(keyword_lower)
    
    # 添加去重后的关键词
    prompt_parts.extend(unique_keywords)

    # 5. 添加风格描述（支持变体）
    style_descriptions = {
        "minimalist": "clean, simple, modern, minimal design",
        "vintage": "retro, classic, vintage style, aged look",
        "modern": "contemporary, sleek, cutting-edge design",
        "cartoon": "playful, fun, cartoon style, colorful",
        "abstract": "abstract, artistic, creative, unique shapes",
        "geometric": "geometric shapes, structured, mathematical",
        "corporate": "professional, business, corporate identity",
        "handdrawn": "hand-drawn, artistic, sketch style, organic"
    }

    # 使用风格变体
    if request.style in STYLE_VARIANTS and variant_index < len(STYLE_VARIANTS[request.style]):
        style_desc = STYLE_VARIANTS[request.style][variant_index]
    else:
        style_desc = style_descriptions.get(request.style, "professional")

    prompt_parts.append(style_desc)

    # 6. 添加行业特定形状描述
    if industry_template.get('shapes'):
        prompt_parts.append(industry_template['shapes'])

    # 7. 添加LOGO特定要求
    prompt_parts.extend([
        "logo design",
        "vector style", 
        "clean background",
        "high quality",
        "professional"
    ])

    # 8. 智能颜色方案选择
    color_scheme = request.color_scheme
    if not color_scheme and industry_template.get('colors'):
        # 如果用户没有选择颜色，使用行业推荐颜色
        color_scheme = industry_template['colors'][variant_index % len(industry_template['colors'])]

    if color_scheme:
        prompt_parts.append(f"{color_scheme} color scheme")

    # 9. 组合所有部分，用逗号分隔
    final_prompt = ", ".join(prompt_parts)
    
    return final_prompt

# 测试函数
if __name__ == "__main__":
    # 创建测试请求
    request = LogoRequest(
        prompt='old prompt should be ignored',
        company_name='TechCorp',
        industry='technology',
        keywords='innovation, digital',
        color_scheme='blue tones',
        style='modern',
        width=1024,
        height=1024
    )

    print("=== 测试 enhance_prompt 函数 ===")
    print(f"输入的原始prompt: '{request.prompt}'")
    print(f"公司名称: {request.company_name}")
    print(f"行业: {request.industry}")
    print(f"关键词: {request.keywords}")
    print(f"颜色方案: {request.color_scheme}")
    print(f"风格: {request.style}")
    print()

    # 测试enhance_prompt函数
    result = enhance_prompt(request)
    print("生成的最终提示词:")
    print(result)
    print()
    print(f"提示词长度: {len(result)}")
    print()
    
    # 检查是否包含原始prompt
    if request.prompt in result:
        print("❌ 错误：最终提示词中仍包含原始prompt")
    else:
        print("✅ 正确：最终提示词中不包含原始prompt")
    
    # 检查是否包含用户参数
    checks = [
        (request.company_name, "公司名称"),
        (request.industry, "行业"),
        (request.color_scheme, "颜色方案"),
        ("modern" in result or "contemporary" in result, "风格")
    ]
    
    print("\n参数包含检查:")
    for check, name in checks:
        if check and (isinstance(check, str) and check in result) or (isinstance(check, bool) and check):
            print(f"✅ {name}: 已包含")
        else:
            print(f"❌ {name}: 未包含")
