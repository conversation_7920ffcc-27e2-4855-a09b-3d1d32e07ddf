@echo off
echo 🚀 启动AI Logo Tool后端测试...

echo 📋 检查Docker状态...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未运行，请先启动Docker Desktop
    pause
    exit /b 1
)

echo ✅ Docker运行正常

echo 🐳 启动简化版服务（仅后端+数据库）...
docker-compose -f docker-compose.simple.yml up -d

echo ⏳ 等待服务启动...
timeout /t 30 /nobreak >nul

echo 📊 检查服务状态...
docker-compose -f docker-compose.simple.yml ps

echo 🔍 测试后端API...
echo.
echo 测试健康检查:
curl -s http://localhost:8000/health
echo.
echo.

echo 测试API文档:
echo 请访问: http://localhost:8000/docs
echo.

echo 测试LOGO生成:
echo 请访问: http://localhost:8000/docs 并测试 /api/generate-logo 接口
echo.

echo 📋 常用命令:
echo   查看日志: docker-compose -f docker-compose.simple.yml logs -f
echo   停止服务: docker-compose -f docker-compose.simple.yml down
echo.

pause
