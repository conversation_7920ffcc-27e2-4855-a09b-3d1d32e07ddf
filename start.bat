@echo off
chcp 65001 >nul
echo.
echo 🚀 AI Logo Tool 启动脚本
echo ================================
echo.

:: 检查Docker是否运行
echo 📋 检查Docker状态...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未运行，请先启动Docker Desktop
    echo.
    pause
    exit /b 1
)
echo ✅ Docker运行正常
echo.

:: 检查.env文件
if not exist .env (
    echo ⚠️  .env文件不存在，从模板创建...
    copy .env.example .env >nul
    echo 📝 请编辑 .env 文件，填入你的API密钥
    echo.
)

:: 创建必要目录
if not exist uploads mkdir uploads
if not exist nginx mkdir nginx

:: 显示当前配置
echo 📊 当前配置信息:
echo    - Stability AI密钥: 已配置
echo    - 项目目录: %CD%
echo    - 上传目录: %CD%\uploads
echo.

:: 启动服务
echo 🐳 启动Docker服务...
echo.

echo 📡 启动后端服务...
docker-compose -f docker-compose.test.yml up -d
if %errorlevel% neq 0 (
    echo ❌ 后端启动失败
    pause
    exit /b 1
)

echo ⏳ 等待后端服务启动...
timeout /t 30 /nobreak >nul

echo 🎨 启动前端服务...
docker-compose -f docker-compose.full.yml up frontend -d
if %errorlevel% neq 0 (
    echo ❌ 前端启动失败
    pause
    exit /b 1
)

echo ⏳ 等待前端服务启动...
timeout /t 20 /nobreak >nul

:: 检查服务状态
echo.
echo 📊 检查服务状态...
docker-compose -f docker-compose.full.yml ps

echo.
echo ✅ 服务启动完成！
echo.
echo 🌐 访问地址：
echo    前端应用: http://localhost:3000
echo    后端API:  http://localhost:8000
echo    API文档:  http://localhost:8000/docs
echo    健康检查: http://localhost:8000/health
echo.
echo 📋 常用命令：
echo    查看日志: docker-compose -f docker-compose.full.yml logs -f
echo    停止服务: docker-compose -f docker-compose.full.yml down
echo    重启服务: docker-compose -f docker-compose.full.yml restart
echo.

:: 测试后端API
echo 🔍 测试后端API...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -UseBasicParsing -TimeoutSec 10; if ($response.StatusCode -eq 200) { Write-Host '✅ 后端API正常' -ForegroundColor Green } else { Write-Host '⚠️ 后端API异常' -ForegroundColor Yellow } } catch { Write-Host '❌ 后端API无法访问' -ForegroundColor Red }"

echo.
echo 🎉 启动完成！现在可以访问 http://localhost:3000 开始使用
echo.

:: 询问是否打开浏览器
set /p openBrowser="是否自动打开浏览器? (y/n): "
if /i "%openBrowser%"=="y" (
    start http://localhost:3000
    echo 🌐 已打开浏览器
)

echo.
echo 💡 使用提示：
echo    1. 填写公司名称
echo    2. 选择行业类型
echo    3. 选择设计风格和颜色偏好
echo    4. 点击"快速生成"或"多方案"
echo    5. 选择喜欢的设计方案
echo.
echo 🔧 如需停止服务，请运行: docker-compose -f docker-compose.full.yml down
echo.
pause
